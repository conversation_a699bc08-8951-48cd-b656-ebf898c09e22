package net.pandaboots.pandabootsstatus.gui;

import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.text.Text;
import net.pandaboots.pandabootsstatus.config.ActionBarConfig;
import net.pandaboots.pandabootsstatus.gui.modern.ModernButton;
import net.pandaboots.pandabootsstatus.gui.modern.ModernSlider;
import net.pandaboots.pandabootsstatus.gui.modern.ModernToggle;
import net.pandaboots.pandabootsstatus.gui.modern.ModernWidget;

import java.util.ArrayList;
import java.util.List;

/**
 * Settings screen for Panda Boots Status using readwork's ModernWidget architecture
 */
public class ActionBarSettingsScreen extends Screen {
    private final Screen parent;
    private final ActionBarConfig config;
    private final List<ModernWidget> widgets = new ArrayList<>();

    // UI Constants from readwork's ModernWidget
    private static final int WIDGET_HEIGHT = 30;
    private static final int WIDGET_SPACING = 8;
    private static final int SECTION_SPACING = 16;
    private static final int PADDING = 20;
    
    public ActionBarSettingsScreen(Screen parent) {
        super(Text.literal("Panda Boots Status Settings"));
        this.parent = parent;
        this.config = ActionBarConfig.getInstance();
    }
    
    @Override
    protected void init() {
        super.init();
        widgets.clear();

        int contentWidth = Math.min(600, this.width - PADDING * 2);
        int contentX = (this.width - contentWidth) / 2;
        int currentY = 60;
        
        // Display Settings Section
        currentY = addSection("Display Settings", contentX, currentY, contentWidth);
        
        // Master toggle
        ModernToggle masterToggle = new ModernToggle(
            contentX, currentY, contentWidth, WIDGET_HEIGHT,
            "🎯 Enable Status Display",
            config::isShowStatusDisplay, config::setShowStatusDisplay
        );
        widgets.add(masterToggle);
        currentY += WIDGET_HEIGHT + WIDGET_SPACING;
        
        // Display options in two columns
        int columnWidth = (contentWidth - WIDGET_SPACING) / 2;
        int rightColumnX = contentX + columnWidth + WIDGET_SPACING;
        
        ModernToggle titleToggle = new ModernToggle(
            contentX, currentY, columnWidth, WIDGET_HEIGHT,
            "🐼 Show Title",
            config::isShowTitle, config::setShowTitle
        );
        widgets.add(titleToggle);
        
        ModernToggle descToggle = new ModernToggle(
            rightColumnX, currentY, columnWidth, WIDGET_HEIGHT,
            "📝 Show Descriptions",
            config::isShowEffectDescriptions, config::setShowEffectDescriptions
        );
        widgets.add(descToggle);
        currentY += WIDGET_HEIGHT + WIDGET_SPACING;
        
        ModernToggle iconsToggle = new ModernToggle(
            contentX, currentY, columnWidth, WIDGET_HEIGHT,
            "😄 Show Mood Icons",
            config::isShowMoodIcons, config::setShowMoodIcons
        );
        widgets.add(iconsToggle);
        
        ModernButton backgroundToggle = new ModernButton(
            rightColumnX, currentY, columnWidth, WIDGET_HEIGHT,
            "🎨 Toggle Background & Border",
            () -> config.setShowBackgroundAndBorder(!config.isShowBackgroundAndBorder()),
            ModernButton.ButtonStyle.SECONDARY
        );
        widgets.add(backgroundToggle);
        currentY += WIDGET_HEIGHT + SECTION_SPACING;
        
        // Position & Size Section
        currentY = addSection("Position & Size", contentX, currentY, contentWidth);
        
        ModernButton positionButton = new ModernButton(
            contentX, currentY, contentWidth, WIDGET_HEIGHT,
            "📍 Change Position & Scale",
            () -> {
                if (this.client != null) {
                    this.client.setScreen(new StatusPositionScreen(this));
                }
            },
            ModernButton.ButtonStyle.PRIMARY
        );
        widgets.add(positionButton);
        currentY += WIDGET_HEIGHT + WIDGET_SPACING;
        
        // Scale slider
        ModernSlider scaleSlider = new ModernSlider(
            contentX, currentY, contentWidth, WIDGET_HEIGHT + 10,
            "📏 Scale", 0.5f, 2.0f,
            config::getStatusScale, config::setStatusScale,
            value -> String.format("%.1fx", value)
        );
        widgets.add(scaleSlider);
        currentY += WIDGET_HEIGHT + 10 + SECTION_SPACING;
        
        // Visual Styling Section
        currentY = addSection("Visual Styling", contentX, currentY, contentWidth);
        
        // Background opacity slider
        ModernSlider opacitySlider = new ModernSlider(
            contentX, currentY, columnWidth, WIDGET_HEIGHT + 10,
            "🎨 Background Opacity", 0.0f, 100.0f,
            () -> (float) config.getBackgroundOpacity(), 
            value -> config.setBackgroundOpacity(value.intValue()),
            value -> String.format("%.0f%%", value)
        );
        widgets.add(opacitySlider);
        
        // Shadow intensity slider
        ModernSlider shadowSlider = new ModernSlider(
            rightColumnX, currentY, columnWidth, WIDGET_HEIGHT + 10,
            "🌑 Shadow Intensity", 0.0f, 100.0f,
            () -> (float) config.getShadowIntensity(),
            value -> config.setShadowIntensity(value.intValue()),
            value -> String.format("%.0f%%", value)
        );
        widgets.add(shadowSlider);
        currentY += WIDGET_HEIGHT + 10 + SECTION_SPACING;

        // Custom Emojis Section
        currentY = addSection("Custom Emojis", contentX, currentY, contentWidth);

        // Button to open Custom Emojis settings
        ModernButton customEmojiButton = new ModernButton(
            contentX, currentY, contentWidth, WIDGET_HEIGHT,
            "🎨 Configure Custom Emojis...",
            () -> {
                if (this.client != null) {
                    this.client.setScreen(new CustomEmojiSettingsScreen(this));
                }
            },
            ModernButton.ButtonStyle.SECONDARY
        );
        widgets.add(customEmojiButton);
        currentY += WIDGET_HEIGHT + SECTION_SPACING;

        // Done button
        ModernButton doneButton = new ModernButton(
            contentX + contentWidth / 2 - 50, currentY, 100, WIDGET_HEIGHT,
            "Done", this::close, ModernButton.ButtonStyle.PRIMARY
        );
        widgets.add(doneButton);
    }
    
    private int addSection(String title, int x, int y, int width) {
        // Section titles are rendered in the render method
        return y + 25; // Space for section title
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Draw modern background
        context.fill(0, 0, this.width, this.height, ModernWidget.BACKGROUND_COLOR);

        // Draw title
        context.drawCenteredTextWithShadow(this.textRenderer, this.title, this.width / 2, 20, ModernWidget.TEXT_COLOR);

        // Draw section titles
        int contentWidth = Math.min(600, this.width - PADDING * 2);
        int contentX = (this.width - contentWidth) / 2;

        context.drawText(this.textRenderer, "Display Settings", contentX, 85, ModernWidget.ACCENT_COLOR, false);
        context.drawText(this.textRenderer, "Position & Size", contentX, 85 + 6 * (WIDGET_HEIGHT + WIDGET_SPACING) + SECTION_SPACING, ModernWidget.ACCENT_COLOR, false);
        context.drawText(this.textRenderer, "Visual Styling", contentX, 85 + 9 * (WIDGET_HEIGHT + WIDGET_SPACING) + 2 * SECTION_SPACING + 10, ModernWidget.ACCENT_COLOR, false);
        context.drawText(this.textRenderer, "Custom Emojis", contentX, 85 + 11 * (WIDGET_HEIGHT + WIDGET_SPACING) + 3 * SECTION_SPACING + 20, ModernWidget.ACCENT_COLOR, false);

        // Render modern widgets
        for (ModernWidget widget : widgets) {
            widget.render(context, mouseX, mouseY, delta);
        }
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // Handle modern widget clicks
        for (ModernWidget widget : widgets) {
            if (widget.mouseClicked(mouseX, mouseY, button)) {
                return true;
            }
        }
        return super.mouseClicked(mouseX, mouseY, button);
    }
    
    @Override
    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        // Handle modern widget dragging
        for (ModernWidget widget : widgets) {
            if (widget.mouseDragged(mouseX, mouseY, button, deltaX, deltaY)) {
                return true;
            }
        }
        return super.mouseDragged(mouseX, mouseY, button, deltaX, deltaY);
    }

    @Override
    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        // Handle modern widget releases
        for (ModernWidget widget : widgets) {
            if (widget.mouseReleased(mouseX, mouseY, button)) {
                return true;
            }
        }
        return super.mouseReleased(mouseX, mouseY, button);
    }



    @Override
    public void close() {
        if (this.client != null) {
            this.client.setScreen(parent);
        }
    }

    @Override
    public boolean shouldPause() {
        return false; // Don't pause the game
    }
}
