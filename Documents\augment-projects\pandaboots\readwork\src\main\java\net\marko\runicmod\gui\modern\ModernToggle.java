package net.marko.runicmod.gui.modern;

import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.text.Text;

import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * Modern toggle switch with smooth animations
 */
public class ModernToggle extends ModernWidget {
    
    private final Supplier<Boolean> valueGetter;
    private final Consumer<Boolean> valueSetter;
    private final String label;
    
    private static final int TOGGLE_WIDTH = 40;
    private static final int TOGGLE_HEIGHT = 20;
    private static final int KNOB_SIZE = 16;
    private static final int KNOB_MARGIN = 2;
    
    public ModernToggle(int x, int y, int width, int height, String label, 
                       Supplier<Boolean> getter, Consumer<Boolean> setter) {
        super(x, y, width, height, Text.literal(label));
        this.label = label;
        this.valueGetter = getter;
        this.valueSetter = setter;
    }
    
    @Override
    protected void renderContent(DrawContext context, int mouseX, int mouseY, float delta) {
        boolean isOn = valueGetter.get();

        // Calculate toggle position first to determine available text space
        int toggleX = getX() + getWidth() - TOGGLE_WIDTH - 8;
        int toggleY = getY() + (getHeight() - TOGGLE_HEIGHT) / 2;

        // Calculate available space for text (leave gap between text and toggle)
        int textGap = 8; // Gap between text and toggle
        int maxTextWidth = toggleX - getX() - 8 - textGap; // Available width for text
        int maxTextHeight = getHeight() - 8; // 4px padding top/bottom

        // Draw label with advanced text fitting
        int textX = getX() + 8;
        int textY = getY() + 4;

        drawFittedText(context, label, textX, textY, maxTextWidth, maxTextHeight, getTextColor(), TextAlignment.LEFT);
        
        // Draw toggle background
        int toggleBgColor = isOn ? SUCCESS_COLOR : SECONDARY_COLOR;
        if (isHovered) {
            toggleBgColor = lightenColor(toggleBgColor, 1.1f);
        }
        
        // Rounded rectangle for toggle background
        context.fill(toggleX, toggleY, toggleX + TOGGLE_WIDTH, toggleY + TOGGLE_HEIGHT, toggleBgColor);
        
        // Draw toggle knob
        int knobX = isOn ? 
            toggleX + TOGGLE_WIDTH - KNOB_SIZE - KNOB_MARGIN : 
            toggleX + KNOB_MARGIN;
        int knobY = toggleY + KNOB_MARGIN;
        
        int knobColor = TEXT_COLOR;
        context.fill(knobX, knobY, knobX + KNOB_SIZE, knobY + KNOB_SIZE, knobColor);
        
        // Draw toggle border
        context.drawBorder(toggleX, toggleY, TOGGLE_WIDTH, TOGGLE_HEIGHT, getBorderColor());
    }
    
    @Override
    protected void renderBackground(DrawContext context, int mouseX, int mouseY, float delta) {
        // Custom background for toggle
        int backgroundColor = isHovered ? HOVER_COLOR : PRIMARY_COLOR;
        context.fill(getX(), getY(), getX() + getWidth(), getY() + getHeight(), backgroundColor);
    }
    
    @Override
    protected void onPress() {
        boolean currentValue = valueGetter.get();
        valueSetter.accept(!currentValue);
    }
    
    /**
     * Lightens a color by a factor
     */
    private int lightenColor(int color, float factor) {
        int r = Math.min(255, (int) (((color >> 16) & 0xFF) * factor));
        int g = Math.min(255, (int) (((color >> 8) & 0xFF) * factor));
        int b = Math.min(255, (int) ((color & 0xFF) * factor));
        int a = (color >> 24) & 0xFF;

        return (a << 24) | (r << 16) | (g << 8) | b;
    }

}
