package net.marko.runicmod.gui;

import net.marko.runicmod.config.RunicModConfig;
import net.marko.runicmod.gui.modern.ModernButton;
import net.marko.runicmod.gui.modern.ModernWidget;
import net.marko.runicmod.rune.RuneType;
import net.marko.runicmod.sound.SoundManager;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.text.Text;
import net.minecraft.client.MinecraftClient;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * Enhanced sound selection screen with individual rune type support
 */
public class EnhancedSoundSelectionScreen extends Screen {
    
    private final Screen parent;
    private final RuneType runeType;
    private final Supplier<String> soundGetter;
    private final Consumer<String> soundSetter;
    private final RunicModConfig config;
    private final List<ModernButton> soundButtons = new ArrayList<>();
    private net.marko.runicmod.gui.modern.ScrollableContainer scrollContainer;
    
    // GUI dimensions
    private static final int GUI_WIDTH = 400;
    private static final int GUI_HEIGHT = 500;
    
    // Common Minecraft sounds with categories
    private static final SoundEntry[] SOUNDS = {
        // Notification sounds
        new SoundEntry("Experience Orb", "minecraft:entity.experience_orb.pickup", "🔮"),
        new SoundEntry("Level Up", "minecraft:entity.player.levelup", "⭐"),
        new SoundEntry("Villager Yes", "minecraft:entity.villager.yes", "✅"),
        new SoundEntry("Villager No", "minecraft:entity.villager.no", "❌"),
        
        // Musical sounds
        new SoundEntry("Note Block Pling", "minecraft:block.note_block.pling", "🎵"),
        new SoundEntry("Note Block Chime", "minecraft:block.note_block.chime", "🔔"),
        new SoundEntry("Note Block Bell", "minecraft:block.note_block.bell", "🛎️"),
        new SoundEntry("Bell Use", "minecraft:block.bell.use", "🔔"),
        
        // Action sounds
        new SoundEntry("Arrow Hit", "minecraft:entity.arrow.hit_player", "🏹"),
        new SoundEntry("Anvil Land", "minecraft:block.anvil.land", "⚒️"),
        new SoundEntry("Explosion", "minecraft:entity.generic.explode", "💥"),
        new SoundEntry("Firework Launch", "minecraft:entity.firework_rocket.launch", "🎆"),
        
        // Ambient sounds
        new SoundEntry("Water Drop", "minecraft:block.pointed_dripstone.drip_water", "💧"),
        new SoundEntry("Click", "minecraft:ui.button.click", "🖱️"),
        new SoundEntry("Pop", "minecraft:entity.item.pickup", "📦"),
        new SoundEntry("Ding", "minecraft:block.note_block.pling", "🔊")
    };
    
    private static class SoundEntry {
        final String name;
        final String soundId;
        final String icon;
        
        SoundEntry(String name, String soundId, String icon) {
            this.name = name;
            this.soundId = soundId;
            this.icon = icon;
        }
    }
    
    public EnhancedSoundSelectionScreen(Screen parent, RuneType runeType, 
                                       Supplier<String> soundGetter, Consumer<String> soundSetter) {
        super(Text.literal("Select Sound - " + runeType.getDisplayName()));
        this.parent = parent;
        this.runeType = runeType;
        this.soundGetter = soundGetter;
        this.soundSetter = soundSetter;
        this.config = RunicModConfig.getInstance();
    }
    
    @Override
    protected void init() {
        super.init();
        soundButtons.clear();

        // Calculate centered position
        int guiX = (width - GUI_WIDTH) / 2;
        int guiY = (height - GUI_HEIGHT) / 2;

        // Create scrollable container for sound buttons
        int scrollAreaHeight = GUI_HEIGHT - 120; // Leave space for title and bottom buttons
        scrollContainer = new net.marko.runicmod.gui.modern.ScrollableContainer(
            guiX + 10, guiY + 60, GUI_WIDTH - 20, scrollAreaHeight
        );

        int buttonWidth = GUI_WIDTH - 60; // Account for scrollbar
        int buttonHeight = 22; // Compact height
        int spacing = 2; // Reduced spacing
        int currentY = guiY + 60;

        // Add sound selection buttons to scrollable container
        for (int i = 0; i < SOUNDS.length; i++) {
            SoundEntry sound = SOUNDS[i];
            final String soundId = sound.soundId; // Capture for lambda

            ModernButton button = new ModernButton(
                guiX + 20, currentY + i * (buttonHeight + spacing),
                buttonWidth, buttonHeight,
                sound.icon + " " + sound.name,
                () -> {
                    selectSound(soundId);
                    testSound(soundId);
                    // Refresh to update button styles
                    init();
                },
                getCurrentSound().equals(sound.soundId) ?
                    ModernButton.ButtonStyle.SUCCESS : ModernButton.ButtonStyle.SECONDARY
            );
            soundButtons.add(button);
            scrollContainer.addChild(button);
        }

        // Fixed position buttons at bottom (not in scroll container)
        ModernButton backButton = new ModernButton(
            guiX + 20, guiY + GUI_HEIGHT - 40,
            120, 30,
            "← Back",
            this::close,
            ModernButton.ButtonStyle.PRIMARY
        );
        addDrawableChild(backButton);

        // Test current sound button (fixed position)
        ModernButton testCurrentButton = new ModernButton(
            guiX + GUI_WIDTH - 140, guiY + GUI_HEIGHT - 40,
            120, 30,
            "🎵 Test Sound",
            () -> testSound(getCurrentSound()),
            ModernButton.ButtonStyle.WARNING
        );
        addDrawableChild(testCurrentButton);
    }
    
    private void selectSound(String soundId) {
        soundSetter.accept(soundId);
        
        // Update button styles to reflect selection
        for (int i = 0; i < soundButtons.size() && i < SOUNDS.length; i++) {
            ModernButton button = soundButtons.get(i);
            SoundEntry sound = SOUNDS[i];
            
            // Update button style based on selection
            // Note: This is a simplified approach - in a full implementation,
            // you'd want to properly update the button's style
        }
    }
    
    private void testSound(String soundId) {
        try {
            // Get the individual volume for this rune type
            float individualVolume = getIndividualVolume();
            float masterVolume = config.getSoundVolume();
            float finalVolume = individualVolume * masterVolume;
            
            SoundManager.playTestSound(soundId, finalVolume);
        } catch (Exception e) {
            // Handle error silently
        }
    }
    
    private float getIndividualVolume() {
        switch (runeType) {
            case RUNIC_OBSTRUCTION:
                return config.getRunicObstructionVolume();
            case SKY_STEPPER:
                return config.getSkyStepperVolume();
            case DASHER:
                return config.getDasherVolume();
            default:
                return 1.0f;
        }
    }
    
    private String getCurrentSound() {
        return soundGetter.get();
    }
    
    @Override
    public void renderBackground(DrawContext context, int mouseX, int mouseY, float delta) {
        // Override to prevent blur
    }
    
    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // Handle scrollable container clicks first
        if (scrollContainer != null && scrollContainer.mouseClicked(mouseX, mouseY, button)) {
            return true;
        }
        return super.mouseClicked(mouseX, mouseY, button);
    }

    @Override
    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        if (scrollContainer != null) {
            scrollContainer.mouseReleased(mouseX, mouseY, button);
        }
        return super.mouseReleased(mouseX, mouseY, button);
    }

    @Override
    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        if (scrollContainer != null && scrollContainer.mouseDragged(mouseX, mouseY, button, deltaX, deltaY)) {
            return true;
        }
        return super.mouseDragged(mouseX, mouseY, button, deltaX, deltaY);
    }

    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double horizontalAmount, double verticalAmount) {
        if (scrollContainer != null && scrollContainer.mouseScrolled(mouseX, mouseY, horizontalAmount, verticalAmount)) {
            return true;
        }
        return super.mouseScrolled(mouseX, mouseY, horizontalAmount, verticalAmount);
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Calculate centered position
        int guiX = (width - GUI_WIDTH) / 2;
        int guiY = (height - GUI_HEIGHT) / 2;

        // Draw background
        context.fill(guiX, guiY, guiX + GUI_WIDTH, guiY + GUI_HEIGHT, ModernWidget.BACKGROUND_COLOR);
        context.drawBorder(guiX, guiY, GUI_WIDTH, GUI_HEIGHT, ModernWidget.BORDER_COLOR);

        // Render scrollable container
        if (scrollContainer != null) {
            scrollContainer.renderWidget(context, mouseX, mouseY, delta);
        }

        // Render fixed buttons
        super.render(context, mouseX, mouseY, delta);

        // Draw title
        var textRenderer = MinecraftClient.getInstance().textRenderer;
        String titleText = "Select Sound for " + runeType.getDisplayName();
        int titleX = guiX + (GUI_WIDTH - textRenderer.getWidth(titleText)) / 2;
        context.drawText(textRenderer, titleText, titleX, guiY + 15, ModernWidget.TEXT_COLOR, false);

        // Draw current selection
        String currentSound = getCurrentSound();
        String currentText = "Current: " + getSoundName(currentSound);
        int currentX = guiX + (GUI_WIDTH - textRenderer.getWidth(currentText)) / 2;
        context.drawText(textRenderer, currentText, currentX, guiY + 35, ModernWidget.ACCENT_COLOR, false);
    }
    
    private String getSoundName(String soundId) {
        for (SoundEntry sound : SOUNDS) {
            if (sound.soundId.equals(soundId)) {
                return sound.name;
            }
        }
        return "Unknown";
    }
    
    @Override
    public boolean shouldPause() {
        return false;
    }
    
    @Override
    public void close() {
        client.setScreen(parent);
    }
}
