info: [2025-06-05T08:18:45Z INFO  rust_launcher::start::bindings] server: None
info: [2025-06-05T08:18:45Z INFO  rust_launcher::start] Loading version
info: {"versionSize":222565505,"state":{"VersionLoading":true},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":0}
info: [2025-06-05T08:18:45Z INFO  rust_launcher::start] Finished loading version
info: [2025-06-05T08:18:45Z INFO  rust_launcher::start] Finished moving mods
info: [2025-06-05T08:18:45Z INFO  rust_launcher::start] Preparing assets
[2025-06-05T08:18:45Z INFO  rust_launcher::start] Preparing JRE
info: {"versionSize":222565505,"state":{"Downloading":["Assets","Jre","Libraries","Mods"]},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":35518}
info: [2025-06-05T08:18:46Z INFO  rust_launcher::start] Preparing libraries
info: [2025-06-05T08:18:46Z INFO  rust_launcher::start] Preparing mods
info: {"versionSize":222565505,"state":{"Downloading":["Assets","Libraries","Mods"]},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":48807807}
info: [2025-06-05T08:18:46Z INFO  rust_launcher::start] Finished preparing mods
info: {"versionSize":222565505,"state":{"Downloading":["Assets","Libraries"]},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":106665354}
info: {"versionSize":222565505,"state":{"Downloading":["Assets"]},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":270385012}
info: [2025-06-05T08:18:46Z INFO  rust_launcher::start] Finished preparing libraries
info: [2025-06-05T08:18:46Z INFO  rust_launcher::start] Finished preparing assets
[2025-06-05T08:18:46Z INFO  rust_launcher::start] Preparing registry
info: [2025-06-05T08:18:46Z INFO  rust_launcher::start] Finished preparing registry
info: {"versionSize":222565505,"state":{"Jvm":true},"currentDownloadRate":3875,"eta":"3 days","expectedTasks":6,"downloadSpeed":3875,"downloaded":**********}
info: [2025-06-05T08:18:46Z INFO  rust_launcher::common::launch] starting JVM with arguments: ["-Djava.library.path=libraries/native\\net/digitalingot/fcef/0.1.1\\extracted/;libraries/native\\net/digitalingot/fwebp/0.0.2\\extracted/;libraries/native\\net/digitalingot/favif/0.0.1\\extracted/;libraries/native\\com/discord/discord-game-sdk/3.2.1\\extracted/;libraries/native\\net/digitalingot/fdiscord/0.0.1\\extracted/;libraries/native\\net/digitalingot/fjni/0.0.2\\extracted/;libraries/native\\net/digitalingot/cef_binary/103.0.0\\extracted/;libraries/native\\org/jitsi/libjitsi-opus-native/1.1-32-g2a5a8171\\extracted/;libraries/native\\org/lwjgl/lwjgl-freetype/3.3.3\\extracted/;libraries/native\\org/lwjgl/lwjgl-glfw/3.3.3\\extracted/;libraries/native\\org/lwjgl/lwjgl-jemalloc/3.3.3\\extracted/;libraries/native\\org/lwjgl/lwjgl-openal/3.3.3\\extracted/;libraries/native\\org/lwjgl/lwjgl-opengl/3.3.3\\extracted/;libraries/native\\org/lwjgl/lwjgl-stb/3.3.3\\extracted/;libraries/native\\org/lwjgl/lwjgl-tinyfd/3.3.3\\extracted/;libraries/native\\org/lwjgl/lwjgl/3.3.3\\extracted/", "--add-opens=java.desktop/java.awt.event=ALL-UNNAMED", "--add-opens=java.desktop/java.awt.color=ALL-UNNAMED", "--add-opens=java.desktop/java.awt=ALL-UNNAMED", "--add-opens=java.base/java.lang=ALL-UNNAMED", "-Xmx8130M", "-XX:+UnlockExperimentalVMOptions", "-XX:+UseG1GC", "-XX:G1NewSizePercent=20", "-XX:G1ReservePercent=20", "-XX:MaxGCPauseMillis=50", "-XX:G1HeapRegionSize=32M", "-Dlog4j2.formatMsgNoLookups=true", "-XX:ErrorFile=feather/java_error.log", "-Djavax.accessibility.assistive_technologies=", "-Djavax.net.ssl.trustStoreType=WINDOWS-ROOT", "-Dfeather.overrideHardwareAccel=false", "-cp", "libraries\\java\\net/minecraft/client/1.21.1/minecraft-1.21.1.jar;libraries\\java\\com/github/oshi/oshi-core/6.4.10/oshi-core-6.4.10.jar;libraries\\java\\com/google/code/gson/gson/2.10.1/gson-2.10.1.jar;libraries\\java\\com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar;libraries\\java\\com/google/guava/guava/32.1.2-jre/guava-32.1.2-jre.jar;libraries\\java\\com/ibm/icu/icu4j/73.2/icu4j-73.2.jar;libraries\\java\\com/mojang/authlib/6.0.54/authlib-6.0.54.jar;libraries\\java\\com/mojang/blocklist/1.0.10/blocklist-1.0.10.jar;libraries\\java\\com/mojang/brigadier/1.3.10/brigadier-1.3.10.jar;libraries\\java\\com/mojang/datafixerupper/8.0.16/datafixerupper-8.0.16.jar;libraries\\java\\com/mojang/logging/1.2.7/logging-1.2.7.jar;libraries\\java\\com/mojang/patchy/2.2.10/patchy-2.2.10.jar;libraries\\java\\com/mojang/text2speech/1.17.9/text2speech-1.17.9.jar;libraries\\java\\commons-codec/commons-codec/1.16.0/commons-codec-1.16.0.jar;libraries\\java\\commons-io/commons-io/2.15.1/commons-io-2.15.1.jar;libraries\\java\\commons-logging/commons-logging/1.2/commons-logging-1.2.jar;libraries\\java\\io/netty/netty-buffer/4.1.97.Final/netty-buffer-4.1.97.Final.jar;libraries\\java\\io/netty/netty-codec/4.1.97.Final/netty-codec-4.1.97.Final.jar;libraries\\java\\io/netty/netty-common/4.1.97.Final/netty-common-4.1.97.Final.jar;libraries\\java\\io/netty/netty-handler/4.1.97.Final/netty-handler-4.1.97.Final.jar;libraries\\java\\io/netty/netty-resolver/4.1.97.Final/netty-resolver-4.1.97.Final.jar;libraries\\java\\io/netty/netty-transport-classes-epoll/4.1.97.Final/netty-transport-classes-epoll-4.1.97.Final.jar;libraries\\java\\io/netty/netty-transport-native-unix-common/4.1.97.Final/netty-transport-native-unix-common-4.1.97.Final.jar;libraries\\java\\io/netty/netty-transport/4.1.97.Final/netty-transport-4.1.97.Final.jar;libraries\\java\\it/unimi/dsi/fastutil/8.5.12/fastutil-8.5.12.jar;libraries\\java\\net/java/dev/jna/jna-platform/5.14.0/jna-platform-5.14.0.jar;libraries\\java\\net/java/dev/jna/jna/5.14.0/jna-5.14.0.jar;libraries\\java\\net/sf/jopt-simple/jopt-simple/5.0.4/jopt-simple-5.0.4.jar;libraries\\java\\org/apache/commons/commons-compress/1.26.0/commons-compress-1.26.0.jar;libraries\\java\\org/apache/commons/commons-lang3/3.14.0/commons-lang3-3.14.0.jar;libraries\\java\\org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar;libraries\\java\\org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar;libraries\\java\\org/apache/logging/log4j/log4j-api/2.22.1/log4j-api-2.22.1.jar;libraries\\java\\org/apache/logging/log4j/log4j-core/2.22.1/log4j-core-2.22.1.jar;libraries\\java\\org/apache/logging/log4j/log4j-slf4j2-impl/2.22.1/log4j-slf4j2-impl-2.22.1.jar;libraries\\java\\org/jcraft/jorbis/0.0.17/jorbis-0.0.17.jar;libraries\\java\\org/joml/joml/1.10.5/joml-1.10.5.jar;libraries\\java\\org/lwjgl/lwjgl-freetype/3.3.3/lwjgl-freetype-3.3.3.jar;libraries\\java\\org/lwjgl/lwjgl-glfw/3.3.3/lwjgl-glfw-3.3.3.jar;libraries\\java\\org/lwjgl/lwjgl-jemalloc/3.3.3/lwjgl-jemalloc-3.3.3.jar;libraries\\java\\org/lwjgl/lwjgl-openal/3.3.3/lwjgl-openal-3.3.3.jar;libraries\\java\\org/lwjgl/lwjgl-opengl/3.3.3/lwjgl-opengl-3.3.3.jar;libraries\\java\\org/lwjgl/lwjgl-stb/3.3.3/lwjgl-stb-3.3.3.jar;libraries\\java\\org/lwjgl/lwjgl-tinyfd/3.3.3/lwjgl-tinyfd-3.3.3.jar;libraries\\java\\org/lwjgl/lwjgl/3.3.3/lwjgl-3.3.3.jar;libraries\\java\\org/lz4/lz4-java/1.8.0/lz4-java-1.8.0.jar;libraries\\java\\org/slf4j/slf4j-api/2.0.9/slf4j-api-2.0.9.jar;libraries\\java\\org/ow2/asm/asm/9.7.1/asm-9.7.1.jar;libraries\\java\\org/ow2/asm/asm-analysis/9.7.1/asm-analysis-9.7.1.jar;libraries\\java\\org/ow2/asm/asm-commons/9.7.1/asm-commons-9.7.1.jar;libraries\\java\\org/ow2/asm/asm-tree/9.7.1/asm-tree-9.7.1.jar;libraries\\java\\org/ow2/asm/asm-util/9.7.1/asm-util-9.7.1.jar;libraries\\java\\net/fabricmc/sponge-mixin/0.15.4+mixin.0.8.7/sponge-mixin-0.15.4+mixin.0.8.7.jar;libraries/java\\net/fabricmc/fabric-loader/0.16.14/fabric-loader-0.16.14.jar;libraries\\java\\net/fabricmc/intermediary/1.21.1/intermediary-1.21.1.jar;libraries\\java\\net/digitalingot/rust-extension/1.0.10/rust-extension-1.0.10.jar;libraries\\java\\net/digitalingot/fjni/0.0.2/fjni-0.0.2.jar;libraries\\java\\net/digitalingot/fdiscord/0.0.1/fdiscord-0.0.1.jar;libraries\\java\\net/digitalingot/fcef/0.1.1/fcef-0.1.1.jar;libraries\\java\\net/digitalingot/fwebp/0.0.1/fwebp-0.0.1.jar;libraries\\java\\net/digitalingot/favif/0.0.1/favif-0.0.1.jar;libraries\\java\\net/digitalingot/feather-server-api/messaging/0.0.5/messaging-0.0.5.jar;libraries\\java\\org/jitsi/libjitsi-opus/1.1-32-g2a5a8171/libjitsi-opus-1.1-32-g2a5a8171.jar;libraries\\java\\org/capnproto/runtime/0.1.10/runtime-0.1.10.jar;libraries\\java\\com/google/inject/guice/5.1.1/guice-5.1.1.jar;libraries\\java\\javassist/javassist/3.12.1.GA/javassist-3.12.1.GA.jar;libraries\\java\\io/sentry/sentry/7.18.1/sentry-7.18.1.jar;libraries\\java\\io/netty/netty-codec-http/4.1.97.Final/netty-codec-http-4.1.97.Final.jar;libraries\\java\\software/bernie/geckolib/fGeckolib-1.21.1-fabric-4.1.0.jar", "-DFabricMcEmu= net.minecraft.client.main.Main ", "net.digitalingot.rustextension.ProxiedStart", "net.fabricmc.loader.launch.knot.KnotClient", "--username", "TheMethodUE", "--version", "1.21.1-feather", "--gameDir", "C:\\Users\\<USER>\\AppData\\Roaming/.minecraft", "--assetsDir", "assets", "--assetIndex", "1.21.1-feather-game", "--uuid", "325c01cfe2704d89903573229de9c097", "--accessToken", "<hidden>", "--userType", "msa", "--versionType", "feather"]
info: Starting cleanup
info: [01:18:47] [main/INFO]: Loading Minecraft 1.21.1 with Fabric Loader 0.16.14
info: [01:18:47] [ForkJoinPool-1-worker-11/WARN]: Mod feather uses the version release/635129ff which isn't compatible with Loader's extended semantic version format (Could not parse version number component 'release/635129ff'!), SemVer is recommended for reliably evaluating dependencies and prioritizing newer version
info: [01:18:47] [main/INFO]: Loading 98 mods:
	- advancedchatcore 1.21.1-1.5.12
	   |-- com_electronwill_night-config_core 3.6.5
	   |-- com_electronwill_night-config_toml 3.6.5
	   |-- com_github_darkkronicle_konstruct_addons 2.0.3-build1
	   |-- com_github_darkkronicle_konstruct_core 2.0.3-build1
	   |-- io_github_maowimpl_owo 2.0.0
	   |-- org_apache_commons_commons-csv 1.8
	   \-- org_mariuszgromada_math_mathparser_org-mxparser 4.4.2
	- advancedchathud 1.21.1-1.3.10
	- appleskin 3.0.6+mc1.21
	- citresewn 1.2.2+1.21
	   \-- citresewn-defaults 1.2.2+1.21
	- cloth-config 15.0.140
	   \-- cloth-basic-math 0.6.1
	- entityculling 1.7.4
	- fabric-api 0.115.1+1.21.1
	   |-- fabric-api-base 0.4.42+6573ed8c19
	   |-- fabric-api-lookup-api-v1 1.6.70+b559734419
	   |-- fabric-biome-api-v1 13.0.31+d527f9fd19
	   |-- fabric-block-api-v1 1.0.22+0af3f5a719
	   |-- fabric-block-view-api-v2 1.0.10+6573ed8c19
	   |-- fabric-blockrenderlayer-v1 1.1.52+0af3f5a719
	   |-- fabric-client-tags-api-v1 1.1.15+6573ed8c19
	   |-- fabric-command-api-v1 1.2.49+f71b366f19
	   |-- fabric-command-api-v2 2.2.28+6ced4dd919
	   |-- fabric-commands-v0 0.2.66+df3654b319
	   |-- fabric-content-registries-v0 8.0.18+b559734419
	   |-- fabric-convention-tags-v1 2.1.3+7f945d5b19
	   |-- fabric-convention-tags-v2 2.10.0+9465b64419
	   |-- fabric-crash-report-info-v1 0.2.29+0af3f5a719
	   |-- fabric-data-attachment-api-v1 1.4.1+9ed317f519
	   |-- fabric-data-generation-api-v1 20.2.26+16c4ae2519
	   |-- fabric-dimensions-v1 4.0.0+6fc22b9919
	   |-- fabric-entity-events-v1 1.7.0+2122d82819
	   |-- fabric-events-interaction-v0 0.7.13+ba9dae0619
	   |-- fabric-game-rule-api-v1 1.0.53+6ced4dd919
	   |-- fabric-item-api-v1 11.1.1+d5debaed19
	   |-- fabric-item-group-api-v1 4.1.6+6823f7cd19
	   |-- fabric-key-binding-api-v1 1.0.47+0af3f5a719
	   |-- fabric-keybindings-v0 0.2.45+df3654b319
	   |-- fabric-lifecycle-events-v1 2.5.0+01d9a51c19
	   |-- fabric-loot-api-v2 3.0.15+3f89f5a519
	   |-- fabric-loot-api-v3 1.0.3+3f89f5a519
	   |-- fabric-message-api-v1 6.0.13+6573ed8c19
	   |-- fabric-model-loading-api-v1 2.0.0+fe474d6b19
	   |-- fabric-networking-api-v1 4.3.0+c7469b2119
	   |-- fabric-object-builder-api-v1 15.2.1+40875a9319
	   |-- fabric-particles-v1 4.0.2+6573ed8c19
	   |-- fabric-recipe-api-v1 5.0.14+248df81c19
	   |-- fabric-registry-sync-v0 5.2.0+34f5d91419
	   |-- fabric-renderer-api-v1 3.4.0+c705a49c19
	   |-- fabric-renderer-indigo 1.7.0+c705a49c19
	   |-- fabric-renderer-registries-v1 3.2.68+df3654b319
	   |-- fabric-rendering-data-attachment-v1 0.3.48+73761d2e19
	   |-- fabric-rendering-fluids-v1 3.1.6+1daea21519
	   |-- fabric-rendering-v0 1.1.71+df3654b319
	   |-- fabric-rendering-v1 5.0.5+df16efd019
	   |-- fabric-resource-conditions-api-v1 4.3.0+8dc279b119
	   |-- fabric-resource-loader-v0 1.3.1+5b5275af19
	   |-- fabric-screen-api-v1 2.0.25+8b68f1c719
	   |-- fabric-screen-handler-api-v1 1.3.88+b559734419
	   |-- fabric-sound-api-v1 1.0.23+6573ed8c19
	   |-- fabric-transfer-api-v1 5.4.2+c24bd99419
info: \-- fabric-transitive-access-wideners-v1 6.2.0+45b9699719
	- fabric-language-kotlin 1.13.3+kotlin.2.1.21
	   |-- org_jetbrains_kotlin_kotlin-reflect 2.1.21
	   |-- org_jetbrains_kotlin_kotlin-stdlib 2.1.21
	   |-- org_jetbrains_kotlin_kotlin-stdlib-jdk7 2.1.21
	   |-- org_jetbrains_kotlin_kotlin-stdlib-jdk8 2.1.21
	   |-- org_jetbrains_kotlinx_atomicfu-jvm 0.27.0
	   |-- org_jetbrains_kotlinx_kotlinx-coroutines-core-jvm 1.10.2
	   |-- org_jetbrains_kotlinx_kotlinx-coroutines-jdk8 1.10.2
	   |-- org_jetbrains_kotlinx_kotlinx-datetime-jvm 0.6.2
	   |-- org_jetbrains_kotlinx_kotlinx-io-bytestring-jvm 0.7.0
	   |-- org_jetbrains_kotlinx_kotlinx-io-core-jvm 0.7.0
	   |-- org_jetbrains_kotlinx_kotlinx-serialization-cbor-jvm 1.8.1
	   |-- org_jetbrains_kotlinx_kotlinx-serialization-core-jvm 1.8.1
	   \-- org_jetbrains_kotlinx_kotlinx-serialization-json-jvm 1.8.1
	- fabricloader 0.16.14
	   \-- mixinextras 0.4.1
	- feather release/635129ff
	- ferritecore 7.0.2-hotfix
	- fix 1.0
	- healthindicator 1.4.0+mc1.21
	- itemscroller 0.24.58
	- java 21
	- libipn 6.5.0
	- lithium 0.15.0+mc1.21.1
	- malilib 0.21.8
	- minecraft 1.21.1
	- minescript 4.0
	- modmenu 11.0.3
	   \-- placeholder-api 2.4.0-pre.2+1.21
	- panda-boots-status 1.0.0
	- sodium 0.6.13+mc1.21.1
	- tweakeroo 0.21.58
	- ukulib 1.3.0+1.21
	   \-- com_moandjiezana_toml_toml4j 0.7.2
info: {"versionSize":222565505,"state":{"Stats":{"memoryUsage":180076784,"cpuUsage":0.2782}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [01:18:47] [main/INFO]: SpongePowered MIXIN Subsystem Version=0.8.7 Source=file:/C:/Users/<USER>/AppData/Roaming/.minecraft/libraries/java/net/fabricmc/sponge-mixin/0.15.4+mixin.0.8.7/sponge-mixin-0.15.4+mixin.0.8.7.jar Service=Knot/Fabric Env=CLIENT
info: [01:18:47] [main/INFO]: Compatibility level set to JAVA_21
info: [01:18:48] [main/INFO]: [Feather::Optifine]: Optifine is not detected
info: [01:18:48] [main/INFO]: [Feather::EssentialMod] Installed: false
info: [01:18:48] [main/WARN]: Mod 'ferritecore' attempted to override option 'mixin.alloc.blockstate', which doesn't exist, ignoring
info: [01:18:48] [main/INFO]: Option 'mixin.entity.collisions.fluid' requires 'mixin.util.block_tracking=true' but found 'false'. Setting 'mixin.entity.collisions.fluid=false'.
info: [01:18:48] [main/INFO]: Option 'mixin.experimental.entity.block_caching.block_support' requires 'mixin.util.block_tracking=true' but found 'false'. Setting 'mixin.experimental.entity.block_caching.block_support=false'.
[01:18:48] [main/INFO]: Option 'mixin.experimental.entity.block_caching.fluid_pushing' requires 'mixin.util.block_tracking=true' but found 'false'. Setting 'mixin.experimental.entity.block_caching.fluid_pushing=false'.
[01:18:48] [main/INFO]: Option 'mixin.experimental.entity.block_caching.block_touching' requires 'mixin.util.block_tracking=true' but found 'false'. Setting 'mixin.experimental.entity.block_caching.block_touching=false'.
[01:18:48] [main/INFO]: Option 'mixin.experimental.entity.block_caching.suffocation' requires 'mixin.util.block_tracking=true' but found 'false'. Setting 'mixin.experimental.entity.block_caching.suffocation=false'.
[01:18:48] [main/INFO]: Option 'mixin.experimental.entity.block_caching' requires 'mixin.util.block_tracking=true' but found 'false'. Setting 'mixin.experimental.entity.block_caching=false'.
info: [01:18:48] [main/INFO]: Loaded configuration file for Lithium: 149 options available, 0 override(s) found
info: [01:18:48] [main/INFO]: Loaded configuration file for Sodium: 43 options available, 0 override(s) found
info: [01:18:48] [main/WARN]: Error loading class: mezz/jei/fabric/platform/RenderHelper (java.lang.ClassNotFoundException: mezz/jei/fabric/platform/RenderHelper)
[01:18:48] [main/WARN]: @Mixin target mezz.jei.fabric.platform.RenderHelper was not found appleskin.jei.mixins.json:JEIRenderHelperMixin from mod appleskin
info: [01:18:49] [SentryExecutorServiceThreadFactory-0/WARN]: Error loading class: net/optifine/Config (java.lang.ClassNotFoundException: net/optifine/Config)
info: {"versionSize":222565505,"state":{"Stats":{"memoryUsage":71399584,"cpuUsage":0.2941}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222565505,"state":{"Stats":{"memoryUsage":83978176,"cpuUsage":0.033}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [01:19:08] [ForkJoinPool-2-worker-6/WARN]: Mod feather uses the version release/635129ff which isn't compatible with Loader's extended semantic version format (Could not parse version number component 'release/635129ff'!), SemVer is recommended for reliably evaluating dependencies and prioritizing newer version
info: {"versionSize":222565505,"state":{"Stats":{"memoryUsage":207248376,"cpuUsage":0.0329}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [01:19:10] [main/INFO]: Searching for graphics cards...
info: [01:19:10] [main/INFO]: Found graphics adapter: AdapterInfo{vendor=NVIDIA, description='NVIDIA GeForce RTX 3080', adapterType=0x0000031B, openglIcdFilePath='C:\WINDOWS\System32\DriverStore\FileRepository\nv_dispi.inf_amd64_9425e4c3b1ac1c47\nvoglv64.dll', openglIcdVersion=32.0.15.6636}
info: [01:19:10] [main/WARN]: Sodium has applied one or more workarounds to prevent crashes or other issues on your system: [NVIDIA_THREADED_OPTIMIZATIONS_BROKEN]
info: [01:19:10] [main/WARN]: This is not necessarily an issue, but it may result in certain features or optimizations being disabled. You can sometimes fix these issues by upgrading your graphics driver.
info: [01:19:10] [main/INFO]: Initializing MixinExtras via com.llamalad7.mixinextras.service.MixinExtrasServiceImpl(version=0.4.1).
info: {"versionSize":222565505,"state":{"Stats":{"memoryUsage":141075856,"cpuUsage":0.115}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222565505,"state":{"Stats":{"memoryUsage":270909616,"cpuUsage":0.2949}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [01:19:12] [Datafixer Bootstrap/INFO]: 226 Datafixer optimizations took 393 milliseconds
info: [01:19:13] [main/WARN]: @Redirect conflict. Skipping mixins.tweakeroo.json:block.MixinNetherPortalBlock from mod tweakeroo->@Redirect::tweakeroo_disablePortalSound(Lnet/minecraft/class_1937;DDDLnet/minecraft/class_3414;Lnet/minecraft/class_3419;FFZ)V with priority 990, already redirected by mixins.feather.json:YEJzK from mod feather->@Redirect::MyuCy(Lnet/minecraft/class_1937;DDDLnet/minecraft/class_3414;Lnet/minecraft/class_3419;FFZ)V with priority 1000
info: {"versionSize":222565505,"state":{"Stats":{"memoryUsage":467277016,"cpuUsage":0.2341}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222565505,"state":{"Stats":{"memoryUsage":287681088,"cpuUsage":0.3834}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: {"versionSize":222565505,"state":{"Stats":{"memoryUsage":268698648,"cpuUsage":0.1868}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [01:19:16] [Render thread/INFO]: Environment: Environment[sessionHost=https://sessionserver.mojang.com, servicesHost=https://api.minecraftservices.com, name=PROD]
info: [01:19:16] [Render thread/INFO]: Setting user: TheMethodUE
info: {"versionSize":222565505,"state":{"Stats":{"memoryUsage":410221808,"cpuUsage":0.3064}},"currentDownloadRate":0,"expectedTasks":6,"downloadSpeed":0,"downloaded":**********}
info: [01:19:17] [Render thread/INFO]: [citresewn] Registering CIT Conditions
info: [01:19:17] [Render thread/INFO]: [citresewn] Registering CIT Types
info: [01:19:17] [Render thread/INFO]: Set key binding: key.entityculling.toggle -> key.keyboard.unknown
info: [01:19:17] [Render thread/INFO]: Set key binding: key.entityculling.toggleBoxes -> key.keyboard.unknown
info: [01:19:17] [Render thread/INFO]: [Indigo] Different rendering plugin detected; not applying Indigo.
info: [01:19:17] [Render thread/INFO]: (minescript) Minescript mod starting...
info: [01:19:17] [Render thread/INFO]: Starting Minescript on OS: Windows 11
info: [01:19:17] [Render thread/INFO]: Deleting undo files from previous run...
info: [01:19:17] [Render thread/INFO]: 0 undo file(s) deleted.
info: [01:19:17] [Render thread/INFO]: Configured file extension `.py` for commands: CommandConfig[extension=.py, command=[C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe, -u, {command}, {args}], environment=[PYTHONPATH=C:\Users\<USER>\AppData\Roaming\.minecraft\minescript\system\lib;C:\Users\<USER>\AppData\Roaming\.minecraft\minescript]]
info: [01:19:17] [Render thread/INFO]: Setting config var: python = "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe" (CommandConfig[extension=.py, command=[C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe, -u, {command}, {args}], environment=[PYTHONPATH=C:\Users\<USER>\AppData\Roaming\.minecraft\minescript\system\lib;C:\Users\<USER>\AppData\Roaming\.minecraft\minescript]])
info: [01:19:17] [Render thread/INFO]: Checking mod updates...
info: [01:19:17] [Render thread/INFO]: Set key binding: key.modmenu.open_menu -> key.keyboard.unknown
info: [01:19:17] [Render thread/INFO]: Initializing Panda Boots Status v1.0.0...
info: [01:19:17] [Render thread/INFO]: Loaded configuration from C:\Users\<USER>\AppData\Roaming\.minecraft\config\panda-boots-status.json
info: [01:19:17] [Render thread/INFO]: Message logger initialized
info: #
info: # A fatal error has been detected by the Java Runtime Environment:
#
#  EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ff86881c75d, pid=45292, tid=48880
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.3+9 (21.0.3+9) (build 21.0.3+9-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.3+9 (21.0.3+9-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# Problematic frame:
info: # C  [lwjgl_opengl.dll+0xc75d]
#
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#
info: # An error report file with more information is saved as:
# feather/java_error.log
info: #
# If you would like to submit a bug report, please visit:
#   https://github.com/adoptium/adoptium-support/issues
# The crash happened outside the Java Virtual Machine in native code.
# See problematic frame for where to report the bug.
#
