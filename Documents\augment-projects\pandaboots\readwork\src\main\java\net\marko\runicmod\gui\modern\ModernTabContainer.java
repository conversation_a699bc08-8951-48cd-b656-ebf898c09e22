package net.marko.runicmod.gui.modern;

import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.widget.ClickableWidget;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

/**
 * Modern tab container with smooth transitions
 */
public class ModernTabContainer {
    
    public static class Tab {
        private final String name;
        private final String icon;
        private final Runnable onSelect;
        
        public Tab(String name, String icon, Runnable onSelect) {
            this.name = name;
            this.icon = icon;
            this.onSelect = onSelect;
        }
        
        public String getName() { return name; }
        public String getIcon() { return icon; }
        public Runnable getOnSelect() { return onSelect; }
    }
    
    private final List<Tab> tabs = new ArrayList<>();
    private int selectedTab = 0;
    private final int x, y, width, height;
    private final int tabHeight = 35;
    
    public ModernTabContainer(int x, int y, int width, int height) {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
    }
    
    public void addTab(String name, String icon, Runnable onSelect) {
        tabs.add(new Tab(name, icon, onSelect));
    }
    
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Draw tab background
        context.fill(x, y, x + width, y + tabHeight, ModernWidget.BACKGROUND_COLOR);
        
        // Draw tabs
        int tabWidth = width / tabs.size();
        for (int i = 0; i < tabs.size(); i++) {
            Tab tab = tabs.get(i);
            int tabX = x + i * tabWidth;
            
            boolean isSelected = i == selectedTab;
            boolean isHovered = mouseX >= tabX && mouseX < tabX + tabWidth && 
                              mouseY >= y && mouseY < y + tabHeight;
            
            // Tab background
            int tabColor = isSelected ? ModernWidget.ACCENT_COLOR : 
                          isHovered ? ModernWidget.HOVER_COLOR : ModernWidget.PRIMARY_COLOR;
            context.fill(tabX, y, tabX + tabWidth, y + tabHeight, tabColor);
            
            // Tab border
            if (i > 0) {
                context.fill(tabX, y, tabX + 1, y + tabHeight, ModernWidget.BORDER_COLOR);
            }
            
            // Tab content
            String displayText = tab.getIcon() + " " + tab.getName();
            var textRenderer = MinecraftClient.getInstance().textRenderer;
            int textWidth = textRenderer.getWidth(displayText);
            int textX = tabX + (tabWidth - textWidth) / 2;
            int textY = y + (tabHeight - textRenderer.fontHeight) / 2;

            int textColor = isSelected ? ModernWidget.TEXT_COLOR : ModernWidget.TEXT_SECONDARY;
            context.drawText(textRenderer, displayText, textX, textY, textColor, false);
            
            // Active tab indicator
            if (isSelected) {
                context.fill(tabX, y + tabHeight - 2, tabX + tabWidth, y + tabHeight, ModernWidget.ACCENT_COLOR);
            }
        }
        
        // Draw content area background
        context.fill(x, y + tabHeight, x + width, y + height, ModernWidget.PRIMARY_COLOR);
        context.drawBorder(x, y + tabHeight, width, height - tabHeight, ModernWidget.BORDER_COLOR);
    }
    
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button == 0 && mouseY >= y && mouseY < y + tabHeight) {
            int tabWidth = width / tabs.size();
            int clickedTab = (int) ((mouseX - x) / tabWidth);
            
            if (clickedTab >= 0 && clickedTab < tabs.size() && clickedTab != selectedTab) {
                selectedTab = clickedTab;
                tabs.get(selectedTab).getOnSelect().run();
                return true;
            }
        }
        return false;
    }
    
    public int getContentX() { return x; }
    public int getContentY() { return y + tabHeight; }
    public int getContentWidth() { return width; }
    public int getContentHeight() { return height - tabHeight; }
    
    public int getSelectedTab() { return selectedTab; }
    public void setSelectedTab(int tab) {
        if (tab >= 0 && tab < tabs.size()) {
            selectedTab = tab;
            tabs.get(selectedTab).getOnSelect().run();
        }
    }

    public String getCurrentTab() {
        if (selectedTab >= 0 && selectedTab < tabs.size()) {
            return tabs.get(selectedTab).getName();
        }
        return "";
    }
}
