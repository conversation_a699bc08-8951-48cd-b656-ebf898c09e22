package net.marko.runicmod.rune;

/**
 * Represents an active rune effect with timing information
 */
public class RuneEffect {
    private final String id;
    private final RuneType type;
    private final String playerName;
    private final int originalDuration;
    private final long startTime;
    private final long endTime;
    private final String tier;

    public RuneEffect(String id, RuneType type, String playerName, int duration, String tier) {
        this.id = id;
        this.type = type;
        this.playerName = playerName;
        this.originalDuration = duration;
        this.startTime = System.currentTimeMillis();
        this.endTime = startTime + (duration * 1000L);
        this.tier = tier;
    }

    public String getId() {
        return id;
    }

    public RuneType getType() {
        return type;
    }

    public String getPlayerName() {
        return playerName;
    }

    public int getOriginalDuration() {
        return originalDuration;
    }

    public long getStartTime() {
        return startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public String getTier() {
        return tier;
    }

    /**
     * Gets the remaining time in seconds
     * @return Remaining seconds, or 0 if expired
     */
    public int getRemainingSeconds() {
        long remaining = endTime - System.currentTimeMillis();
        return Math.max(0, (int) (remaining / 1000) + 1);
    }

    /**
     * Checks if this effect has expired
     * @return true if expired, false otherwise
     */
    public boolean isExpired() {
        return System.currentTimeMillis() > endTime;
    }

    /**
     * Gets the display text for this effect
     * @return Formatted display text
     */
    public String getDisplayText() {
        return type.getDisplayText(playerName, getRemainingSeconds());
    }

    /**
     * Gets the progress of this effect (0.0 to 1.0)
     * @return Progress value
     */
    public float getProgress() {
        long elapsed = System.currentTimeMillis() - startTime;
        long total = endTime - startTime;
        return Math.min(1.0f, (float) elapsed / total);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        RuneEffect that = (RuneEffect) obj;
        return id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }
}
