package net.pandaboots.actionbarlogger.test;

import net.pandaboots.actionbarlogger.status.MoodStatus;

/**
 * Simple test class to verify mood detection logic
 * This is for development testing only
 */
public class MoodDetectionTest {
    
    public static void runTests() {
        System.out.println("=== Enhanced Mood Detection Tests ===");

        // Test all tier levels with all mood types
        testMessage("Mood Swings I has made you feel lazy!");
        testMessage("Mood Swings II has made you feel aggressive!");
        testMessage("Mood Swings III has made you feel playful!");
        testMessage("Mood Swings IV has made you feel lazy!");
        testMessage("Mood Swings V has made you feel aggressive!");

        // Test case variations
        testMessage("mood swings iv has made you feel lazy!");
        testMessage("MOOD SWINGS V HAS MADE YOU FEEL AGGRESSIVE!");
        testMessage("Mood Swings I has made you feel PLAYFUL!");

        // Test edge cases
        testMessage("Mood Swings VI has made you feel lazy!"); // Invalid tier
        testMessage("Mood Swings has made you feel lazy!"); // Missing tier
        testMessage("Mood Swings IV has made you feel happy!"); // Invalid mood

        // Test non-mood messages
        testMessage("Health: 20/20");
        testMessage("XP: 1250/2000");
        testMessage("Some other action bar message");
        testMessage(null);
        testMessage("");

        System.out.println("=== Tests Complete ===");
    }
    
    private static void testMessage(String message) {
        System.out.println("\nTesting: \"" + message + "\"");

        boolean isMoodMessage = MoodStatus.isMoodSwingMessage(message);
        System.out.println("  Is mood message: " + isMoodMessage);

        if (isMoodMessage) {
            MoodStatus.MoodParseResult result = MoodStatus.parseMessage(message);
            if (result != null) {
                MoodStatus status = result.getStatus();
                int tier = result.getTier();
                System.out.println("  Detected status: " + status.getDisplayName());
                System.out.println("  Tier: " + tier);
                System.out.println("  Color: #" + Integer.toHexString(status.getColor()).toUpperCase());
                System.out.println("  Display text: " + status.getDisplayText(tier, 7));
                System.out.println("  Effect description: " + status.getEffectDescription(tier));
            } else {
                System.out.println("  ERROR: No status/tier detected despite being mood message!");
            }
        }
    }
}
