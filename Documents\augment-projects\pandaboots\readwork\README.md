# Rune Utils

A Minecraft Fabric mod that detects and displays rune effects including Rune Obstruction, Sky Stepper, and Dash<PERSON> with modern GUI and customizable settings.

**Author:** Cerv

## Project Structure

### Core Files

- **[build.gradle](build.gradle)** - Gradle build configuration for the mod
- **[gradle.properties](gradle.properties)** - Project properties and version information
- **[settings.gradle](settings.gradle)** - Gradle settings
- **[fabric.mod.json](src/main/resources/fabric.mod.json)** - Mod metadata and configuration

### Source Code

#### Main Classes

- **[RunicMod.java](src/main/java/net/marko/runicmod/RunicMod.java)** - Main mod class that initializes the mod
- **[ChatListener.java](src/main/java/net/marko/runicmod/ChatListener.java)** - Handles chat message detection and processing
- **[RunicModDataGenerator.java](src/main/java/net/marko/runicmod/RunicModDataGenerator.java)** - Data generation for the mod

#### Mixins

- **[ExampleMixin.java](src/main/java/net/marko/runicmod/mixin/ExampleMixin.java)** - Example mixin class
- **[runic-mod.mixins.json](src/main/resources/runic-mod.mixins.json)** - Mixin configuration

### Resources

- **[icon.png](src/main/resources/assets/runic-mod/icon.png)** - Mod icon

## Features

Rune Utils provides comprehensive rune effect detection and display:

### Core Features
1. **Multi-Rune Detection**: Detects Rune Obstruction, Sky Stepper, and Dasher effects
2. **Modern GUI**: Clean, responsive settings interface with tabbed layout
3. **Customizable Display**: Adjustable text position, scale, and colors
4. **Audio Notifications**: Individual sound settings for each rune type
5. **Real-time Effects**: Live countdown timers and visual indicators

### Advanced Features
- **Smart Chat Processing**: Detects multiple message formats and patterns
- **Distance-based Display**: Shows effects only for nearby players
- **Scrollable Interfaces**: Smooth scrolling for sound selection and settings
- **Text Fitting System**: Intelligent text wrapping, scaling, and truncation
- **Drag & Drop Positioning**: Interactive text positioning with live preview

## Development

This mod is built using:
- Minecraft 1.21.1
- Fabric Loader
- Java 21

## Building

To build the mod:
```bash
./gradlew build
```

The compiled JAR will be in the `build/libs` directory.

## Installation

1. Install Fabric Loader for Minecraft 1.21.1
2. Place the mod JAR in your Minecraft `mods` folder
3. Launch Minecraft with Fabric

## License

This project is licensed under CC0-1.0 - see the [LICENSE](LICENSE) file for details.
