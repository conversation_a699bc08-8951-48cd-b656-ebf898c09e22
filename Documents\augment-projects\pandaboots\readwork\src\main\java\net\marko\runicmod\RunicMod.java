package net.marko.runicmod;

import net.fabricmc.api.ClientModInitializer;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientLifecycleEvents;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.fabricmc.fabric.api.client.rendering.v1.HudRenderCallback;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.fabricmc.fabric.api.client.screen.v1.ScreenMouseEvents;
import net.marko.runicmod.config.RunicModConfig;
import net.marko.runicmod.networking.RunicNetworking;
import net.marko.runicmod.render.CustomTextRenderer;
import net.marko.runicmod.render.FontManager;
import net.marko.runicmod.gui.ModSettingsScreen;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;
import org.lwjgl.glfw.GLFW;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class RunicMod implements ClientModInitializer {
	public static final Logger LOGGER = LoggerFactory.getLogger("RuneUtils");

	// Set to true to enable debug features like sending chat messages
	public static final boolean DEBUG_MODE = false;

	// Keybinding for opening settings
	private static KeyBinding settingsKeyBinding;

	@Override
	public void onInitializeClient() {
		LOGGER.info("Initializing Runic Mod v2.0.0...");

		// Initialize font system
		FontManager.initialize();

		// Load config
        RunicModConfig.getInstance();

		// Register keybinding for settings
		settingsKeyBinding = KeyBindingHelper.registerKeyBinding(new KeyBinding(
			"key.runic-mod.settings",
			InputUtil.Type.KEYSYM,
			GLFW.GLFW_KEY_R,
			"category.runic-mod"
		));

		// Register the chat listener
		ChatListener.register();

		// Register networking
		RunicNetworking.registerNetworking();

		// Register tick events
		ClientTickEvents.END_CLIENT_TICK.register(client -> {
			// Check for settings key press
			if (settingsKeyBinding.wasPressed()) {
				client.setScreen(new ModSettingsScreen(client.currentScreen));
			}

			// Handle mouse input for dragging (only when no screen is open)
			if (client.currentScreen == null) {
				handleMouseInput(client);
			}
		});

		// Register HUD renderer for custom text
        HudRenderCallback.EVENT.register((drawContext, tickDelta) -> {
            CustomTextRenderer.renderEffects(drawContext);
        });

		// Register shutdown hook
		ClientLifecycleEvents.CLIENT_STOPPING.register(client -> {
			LOGGER.info("Shutting down Rune Utils...");
			ChatListener.shutdown();
			TitleManager.shutdown();
			CustomTextRenderer.clearAllEffects();
		});

		LOGGER.info("Rune Utils v2.0.0 initialized!");
	}

	/**
	 * Handles mouse input for dragging the text box
	 * @param client The Minecraft client
	 */
	private static void handleMouseInput(MinecraftClient client) {
		if (client.mouse == null) return;

		double mouseX = client.mouse.getX() * client.getWindow().getScaledWidth() / client.getWindow().getWidth();
		double mouseY = client.mouse.getY() * client.getWindow().getScaledHeight() / client.getWindow().getHeight();

		// Check for mouse button state
		boolean leftPressed = GLFW.glfwGetMouseButton(client.getWindow().getHandle(), GLFW.GLFW_MOUSE_BUTTON_LEFT) == GLFW.GLFW_PRESS;

		if (leftPressed) {
			if (!CustomTextRenderer.isDragging()) {
				// Try to start dragging
				CustomTextRenderer.handleMouseClick(mouseX, mouseY);
			} else {
				// Continue dragging
				CustomTextRenderer.handleMouseDrag(mouseX, mouseY);
			}
		} else {
			// Mouse released
			if (CustomTextRenderer.isDragging()) {
				CustomTextRenderer.handleMouseRelease();
			}
		}
	}

	/**
	 * Gets the settings keybinding
	 * @return The settings keybinding
	 */
	public static KeyBinding getSettingsKeyBinding() {
		return settingsKeyBinding;
	}
}