package net.marko.runicmod.gui;

import net.marko.runicmod.RunicMod;
import net.marko.runicmod.config.RunicModConfig;
import net.marko.runicmod.rune.RuneType;
import net.marko.runicmod.sound.SoundManager;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.text.Text;
import org.slf4j.Logger;

/**
 * Screen for selecting sounds for different rune types
 */
public class SoundSelectionScreen extends Screen {
    private static final Logger LOGGER = RunicMod.LOGGER;
    
    private final Screen parent;
    private final RuneType runeType;
    private final RunicModConfig config;
    
    // Available Minecraft sounds
    private static final String[] MINECRAFT_SOUNDS = {
        "minecraft:entity.experience_orb.pickup",
        "minecraft:entity.player.levelup",
        "minecraft:block.note_block.pling",
        "minecraft:block.note_block.bell",
        "minecraft:entity.arrow.hit_player",
        "minecraft:block.anvil.land",
        "minecraft:entity.generic.explode",
        "minecraft:entity.firework_rocket.blast",
        "minecraft:entity.lightning_bolt.thunder",
        "minecraft:block.beacon.activate",
        "minecraft:block.beacon.power_select",
        "minecraft:entity.ender_dragon.death",
        "minecraft:entity.wither.spawn",
        "minecraft:entity.player.attack.crit",
        "minecraft:entity.player.attack.strong"
    };
    
    private static final String[] SOUND_NAMES = {
        "Experience Orb",
        "Level Up",
        "Note Block Pling",
        "Note Block Bell", 
        "Arrow Hit",
        "Anvil Land",
        "Explosion",
        "Firework Blast",
        "Lightning",
        "Beacon Activate",
        "Beacon Power",
        "Ender Dragon Death",
        "Wither Spawn",
        "Critical Hit",
        "Strong Attack"
    };
    
    // UI Constants
    private static final int BUTTON_HEIGHT = 20;
    private static final int BUTTON_SPACING = 25;
    private static final int CONTENT_MARGIN = 20;
    private static final int BACKGROUND_COLOR = 0x80000000;
    private static final int CONTENT_BACKGROUND = 0xFF252526;
    private static final int TEXT_COLOR = 0xFFFFFFFF;
    
    public SoundSelectionScreen(Screen parent, RuneType runeType) {
        super(Text.literal("Select Sound - " + runeType.getDisplayName()));
        this.parent = parent;
        this.runeType = runeType;
        this.config = RunicModConfig.getInstance();
    }
    
    @Override
    protected void init() {
        super.init();
        
        int contentY = CONTENT_MARGIN;
        int contentX = CONTENT_MARGIN;
        int buttonWidth = width - CONTENT_MARGIN * 2;
        
        // Add sound selection buttons
        for (int i = 0; i < MINECRAFT_SOUNDS.length; i++) {
            final String soundId = MINECRAFT_SOUNDS[i];
            final String soundName = SOUND_NAMES[i];
            
            // Check if this is the currently selected sound
            String currentSound = getCurrentSound();
            boolean isSelected = soundId.equals(currentSound);
            String buttonText = soundName + (isSelected ? " ✓" : "");
            
            addDrawableChild(ButtonWidget.builder(Text.literal(buttonText), button -> {
                setCurrentSound(soundId);
                clearAndInit(); // Refresh to update checkmarks
            }).dimensions(contentX, contentY, buttonWidth - 100, BUTTON_HEIGHT).build());
            
            // Add test button
            addDrawableChild(ButtonWidget.builder(Text.literal("Test"), button -> {
                SoundManager.playMinecraftSound(soundId, config.getSoundVolume());
            }).dimensions(contentX + buttonWidth - 90, contentY, 80, BUTTON_HEIGHT).build());
            
            contentY += BUTTON_SPACING;
        }
        
        // Add custom sound section
        contentY += 10;
        
        // Custom sound toggle
        addDrawableChild(ButtonWidget.builder(
            Text.literal("Use Custom Sound: " + (config.isUseCustomSound() ? "ON" : "OFF")),
            button -> {
                config.setUseCustomSound(!config.isUseCustomSound());
                button.setMessage(Text.literal("Use Custom Sound: " + (config.isUseCustomSound() ? "ON" : "OFF")));
            }
        ).dimensions(contentX, contentY, buttonWidth / 2, BUTTON_HEIGHT).build());
        
        contentY += BUTTON_SPACING;
        
        // Custom sound path (simplified - in a full implementation this would open a file dialog)
        addDrawableChild(ButtonWidget.builder(
            Text.literal("Select Custom Sound File"),
            button -> {
                // In a full implementation, this would open a file dialog
                // For now, just show a message
                LOGGER.info("Custom sound file selection not yet implemented");
            }
        ).dimensions(contentX, contentY, buttonWidth / 2, BUTTON_HEIGHT).build());
        
        // Bottom buttons
        int buttonY = height - 40;
        
        // Done button
        addDrawableChild(ButtonWidget.builder(Text.literal("Done"), button -> {
            close();
        }).dimensions(width - 90, buttonY, 80, BUTTON_HEIGHT).build());
        
        // Cancel button
        addDrawableChild(ButtonWidget.builder(Text.literal("Cancel"), button -> {
            close();
        }).dimensions(width - 180, buttonY, 80, BUTTON_HEIGHT).build());
    }
    
    private String getCurrentSound() {
        switch (runeType) {
            case RUNIC_OBSTRUCTION:
                return config.getSelectedSound();
            case SKY_STEPPER:
                return config.getSkyStepperSound();
            case DASHER:
                return config.getDasherSound();
            default:
                return config.getSelectedSound();
        }
    }
    
    private void setCurrentSound(String soundId) {
        switch (runeType) {
            case RUNIC_OBSTRUCTION:
                config.setSelectedSound(soundId);
                break;
            case SKY_STEPPER:
                config.setSkyStepperSound(soundId);
                break;
            case DASHER:
                config.setDasherSound(soundId);
                break;
        }
    }
    
    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Draw background
        context.fill(0, 0, width, height, BACKGROUND_COLOR);
        context.fill(CONTENT_MARGIN, CONTENT_MARGIN, width - CONTENT_MARGIN, height - 50, CONTENT_BACKGROUND);
        
        super.render(context, mouseX, mouseY, delta);
        
        // Draw title
        context.drawCenteredTextWithShadow(textRenderer, title, width / 2, 8, TEXT_COLOR);
        
        // Draw current selection info
        String currentSound = getCurrentSound();
        String currentName = "Unknown";
        for (int i = 0; i < MINECRAFT_SOUNDS.length; i++) {
            if (MINECRAFT_SOUNDS[i].equals(currentSound)) {
                currentName = SOUND_NAMES[i];
                break;
            }
        }
        
        String infoText = "Current: " + currentName;
        context.drawTextWithShadow(textRenderer, infoText, CONTENT_MARGIN + 5, height - 60, TEXT_COLOR);
    }
    
    @Override
    public void close() {
        client.setScreen(parent);
    }
}
