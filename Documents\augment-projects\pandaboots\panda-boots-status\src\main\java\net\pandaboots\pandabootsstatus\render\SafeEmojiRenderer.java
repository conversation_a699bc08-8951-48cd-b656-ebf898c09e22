package net.pandaboots.pandabootsstatus.render;

import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.util.Identifier;
import net.pandaboots.pandabootsstatus.PandaBootsStatus;
import net.pandaboots.pandabootsstatus.status.MoodStatus;

/**
 * Safe emoji renderer that falls back to Unicode or simple text if PNG textures fail
 */
public class SafeEmojiRenderer {
    private static boolean externalPngAvailable = false;
    private static boolean generatedPngAvailable = false;
    private static boolean customEmojiAvailable = false;
    private static boolean checkedRenderers = false;
    
    /**
     * Checks which emoji renderers are available and working
     */
    private static void checkRendererAvailability() {
        if (checkedRenderers) return;

        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client == null) {
                PandaBootsStatus.LOGGER.info("Client not ready, deferring emoji renderer check");
                return;
            }

            // Check custom emoji loader first (highest priority)
            try {
                CustomEmojiLoader.initialize();
                customEmojiAvailable = CustomEmojiLoader.hasCustomEmoji("panda") ||
                                     CustomEmojiLoader.hasCustomEmoji("aggressive") ||
                                     CustomEmojiLoader.hasCustomEmoji("playful") ||
                                     CustomEmojiLoader.hasCustomEmoji("lazy");

                if (customEmojiAvailable) {
                    PandaBootsStatus.LOGGER.info("Custom emoji loader enabled");
                } else {
                    PandaBootsStatus.LOGGER.info("No custom emojis configured or loaded");
                }
            } catch (Exception e) {
                PandaBootsStatus.LOGGER.warn("Custom emoji loader check failed: {}", e.getMessage());
                customEmojiAvailable = false;
            }

            // Check external PNG files second (preferred) - try improved loader
            try {
                ImprovedExternalPngRenderer.initialize();

                // Check each emoji individually for better debugging
                // Note: PNG files should be named: aggressive.png, playful.png, lazy.png, panda.png
                boolean pandaAvailable = ImprovedExternalPngRenderer.hasEmojiTexture("panda");
                boolean aggressiveAvailable = ImprovedExternalPngRenderer.hasEmojiTexture("aggressive");
                boolean playfulAvailable = ImprovedExternalPngRenderer.hasEmojiTexture("playful");
                boolean lazyAvailable = ImprovedExternalPngRenderer.hasEmojiTexture("lazy");

                externalPngAvailable = pandaAvailable || aggressiveAvailable || playfulAvailable || lazyAvailable;

                PandaBootsStatus.LOGGER.info("Improved External PNG emoji availability check:");
                PandaBootsStatus.LOGGER.info("  panda: {}", pandaAvailable ? "Available" : "Missing");
                PandaBootsStatus.LOGGER.info("  aggressive: {}", aggressiveAvailable ? "Available" : "Missing");
                PandaBootsStatus.LOGGER.info("  playful: {}", playfulAvailable ? "Available" : "Missing");
                PandaBootsStatus.LOGGER.info("  lazy: {}", lazyAvailable ? "Available" : "Missing");

                if (externalPngAvailable) {
                    PandaBootsStatus.LOGGER.info("Improved external PNG emoji system enabled ({} textures loaded)", ImprovedExternalPngRenderer.getLoadedTextureCount());
                } else {
                    PandaBootsStatus.LOGGER.info("No external PNG emoji files found, trying fallback approach");

                    // Try the original external PNG renderer as backup
                    ExternalPngEmojiRenderer.initialize();
                    externalPngAvailable = ExternalPngEmojiRenderer.hasEmojiTexture("panda") ||
                                         ExternalPngEmojiRenderer.hasEmojiTexture("aggressive") ||
                                         ExternalPngEmojiRenderer.hasEmojiTexture("playful") ||
                                         ExternalPngEmojiRenderer.hasEmojiTexture("lazy");

                    if (externalPngAvailable) {
                        PandaBootsStatus.LOGGER.info("Original external PNG emoji system enabled as fallback");
                    }
                }
            } catch (Exception e) {
                PandaBootsStatus.LOGGER.warn("External PNG emoji check failed: {}", e.getMessage());
                externalPngAvailable = false;
            }

            // Check generated PNG renderer as fallback
            if (!externalPngAvailable) {
                try {
                    if (client.isOnThread()) {
                        PngEmojiRenderer.initialize();
                        generatedPngAvailable = true;
                        PandaBootsStatus.LOGGER.info("Generated PNG emoji renderer is available");
                    } else {
                        PandaBootsStatus.LOGGER.info("Generated PNG emoji renderer not ready");
                        generatedPngAvailable = false;
                    }
                } catch (Exception e) {
                    PandaBootsStatus.LOGGER.warn("Generated PNG emoji renderer failed: {}", e.getMessage());
                    generatedPngAvailable = false;
                }
            }

            checkedRenderers = true;

        } catch (Exception e) {
            PandaBootsStatus.LOGGER.error("Failed to check emoji renderer availability: {}", e.getMessage());
            checkedRenderers = true;
        }
    }
    
    /**
     * Renders an emoji using the best available method
     */
    public static void renderEmoji(DrawContext context, String emojiName, int x, int y, float scale) {
        checkRendererAvailability();

        try {
            // Try custom emojis first (highest priority)
            if (customEmojiAvailable && CustomEmojiLoader.hasCustomEmoji(emojiName)) {
                PandaBootsStatus.LOGGER.debug("Attempting to render {} with custom emoji", emojiName);
                if (renderCustomEmoji(context, emojiName, x, y, scale)) {
                    PandaBootsStatus.LOGGER.debug("Successfully rendered {} with custom emoji", emojiName);
                    return; // Successfully rendered with custom emoji
                }
                PandaBootsStatus.LOGGER.debug("Custom emoji failed for {}, trying fallback", emojiName);
            }

            // Try external PNG files second (preferred)
            if (externalPngAvailable) {
                PandaBootsStatus.LOGGER.debug("Attempting to render {} with external PNG", emojiName);

                // Try improved external PNG renderer first
                if (ImprovedExternalPngRenderer.renderEmoji(context, emojiName, x, y, scale)) {
                    PandaBootsStatus.LOGGER.debug("Successfully rendered {} with improved external PNG", emojiName);
                    return; // Successfully rendered with improved external PNG
                }

                // Try original external PNG renderer as backup
                if (ExternalPngEmojiRenderer.renderEmoji(context, emojiName, x, y, scale)) {
                    PandaBootsStatus.LOGGER.debug("Successfully rendered {} with original external PNG", emojiName);
                    return; // Successfully rendered with original external PNG
                }

                PandaBootsStatus.LOGGER.debug("Both external PNG methods failed for {}, trying fallback", emojiName);
            }

            // Try generated PNG renderer as fallback
            if (generatedPngAvailable) {
                PandaBootsStatus.LOGGER.debug("Attempting to render {} with generated PNG", emojiName);
                PngEmojiRenderer.renderEmoji(context, emojiName, x, y, scale);
                return; // Successfully rendered with generated PNG
            }

            // Fall back to Unicode/text
            PandaBootsStatus.LOGGER.debug("Using Unicode/text fallback for {}", emojiName);
            renderFallbackEmoji(context, emojiName, x, y, scale);

        } catch (Exception e) {
            PandaBootsStatus.LOGGER.warn("Failed to render emoji {}, using text fallback: {}", emojiName, e.getMessage());
            renderTextFallback(context, emojiName, x, y, scale);
        }
    }

    /**
     * Renders a custom emoji from URL/file
     */
    private static boolean renderCustomEmoji(DrawContext context, String emojiName, int x, int y, float scale) {
        try {
            Identifier textureId = CustomEmojiLoader.getCustomEmojiTexture(emojiName);
            if (textureId != null) {
                int size = (int) (8 * scale); // Half size emojis
                context.drawTexture(textureId, x, y, 0, 0, size, size, size, size);
                return true;
            }
        } catch (Exception e) {
            PandaBootsStatus.LOGGER.warn("Failed to render custom emoji {}: {}", emojiName, e.getMessage());
        }
        return false;
    }

    /**
     * Renders emoji using Unicode fallback
     */
    private static void renderFallbackEmoji(DrawContext context, String emojiName, int x, int y, float scale) {
        String unicodeEmoji = getUnicodeEmoji(emojiName);
        if (unicodeEmoji != null) {
            try {
                FontManager.drawText(context, unicodeEmoji, x, y, 0xFFFFFFFF, scale, false);
            } catch (Exception e) {
                PandaBootsStatus.LOGGER.warn("Unicode emoji failed for {}, using text: {}", emojiName, e.getMessage());
                renderTextFallback(context, emojiName, x, y, scale);
            }
        } else {
            renderTextFallback(context, emojiName, x, y, scale);
        }
    }
    
    /**
     * Renders emoji using simple text fallback
     */
    private static void renderTextFallback(DrawContext context, String emojiName, int x, int y, float scale) {
        String textFallback = getTextFallback(emojiName);
        try {
            FontManager.drawText(context, textFallback, x, y, getEmojiColor(emojiName), scale, false);
        } catch (Exception e) {
            PandaBootsStatus.LOGGER.error("All emoji rendering methods failed for {}: {}", emojiName, e.getMessage());
        }
    }
    
    /**
     * Gets Unicode emoji for the given name
     * Updated to match new emoji names: aggressive, lazy, playful
     */
    private static String getUnicodeEmoji(String emojiName) {
        switch (emojiName) {
            case "panda": return "🐼";
            case "aggressive": return "😡";  // Updated from "angry"
            case "lazy": return "😴";        // Updated from "sleepy"
            case "playful": return "😄";     // Updated from "happy"
            default: return null;
        }
    }
    
    /**
     * Gets text fallback for the given emoji name
     * Updated to match new emoji names: aggressive, lazy, playful
     */
    private static String getTextFallback(String emojiName) {
        switch (emojiName) {
            case "panda": return "[P]";
            case "aggressive": return "[!]";  // Updated from "angry"
            case "lazy": return "[z]";        // Updated from "sleepy"
            case "playful": return "[^]";     // Updated from "happy"
            default: return "[?]";
        }
    }
    
    /**
     * Gets color for text fallback
     * Updated to match new emoji names: aggressive, lazy, playful
     */
    private static int getEmojiColor(String emojiName) {
        switch (emojiName) {
            case "panda": return 0xFFFFFFFF;       // White
            case "aggressive": return 0xFFFF4444;  // Red (updated from "angry")
            case "lazy": return 0xFF4444FF;        // Blue (updated from "sleepy")
            case "playful": return 0xFF44FF44;     // Green (updated from "happy")
            default: return 0xFFFFFFFF;           // White
        }
    }
    
    /**
     * Gets the emoji name for a mood status
     * Updated to match PNG filenames: aggressive.png, lazy.png, playful.png
     */
    public static String getEmojiName(MoodStatus status) {
        String emojiName;
        switch (status) {
            case AGGRESSIVE:
                emojiName = "aggressive";  // Maps to aggressive.png
                break;
            case LAZY:
                emojiName = "lazy";        // Maps to lazy.png
                break;
            case PLAYFUL:
                emojiName = "playful";     // Maps to playful.png
                break;
            default:
                emojiName = null;
                break;
        }

        PandaBootsStatus.LOGGER.debug("SafeEmojiRenderer: Mood {} maps to emoji name: {}", status, emojiName);
        return emojiName;
    }
    
    /**
     * Renders a mood emoji
     */
    public static void renderMoodEmoji(DrawContext context, MoodStatus status, int x, int y, float scale) {
        String emojiName = getEmojiName(status);
        if (emojiName != null) {
            renderEmoji(context, emojiName, x, y, scale);
        }
    }
    
    /**
     * Renders the panda emoji
     */
    public static void renderPandaEmoji(DrawContext context, int x, int y, float scale) {
        renderEmoji(context, "panda", x, y, scale);
    }
    
    /**
     * Gets the width of an emoji when rendered
     */
    public static int getEmojiWidth(float scale) {
        checkRendererAvailability();

        if (customEmojiAvailable) {
            return (int) (8 * scale); // Custom emoji size
        } else if (externalPngAvailable) {
            return ImprovedExternalPngRenderer.getEmojiWidth(scale);
        } else if (generatedPngAvailable) {
            return PngEmojiRenderer.getEmojiWidth(scale);
        } else {
            // Fallback to text width estimation - half size for subtle emojis
            return (int) (6 * scale); // Approximate width (half from original 12 to 6)
        }
    }

    /**
     * Gets the height of an emoji when rendered
     */
    public static int getEmojiHeight(float scale) {
        checkRendererAvailability();

        if (customEmojiAvailable) {
            return (int) (8 * scale); // Custom emoji size
        } else if (externalPngAvailable) {
            return ImprovedExternalPngRenderer.getEmojiHeight(scale);
        } else if (generatedPngAvailable) {
            return PngEmojiRenderer.getEmojiHeight(scale);
        } else {
            // Fallback to text height estimation - half size for subtle emojis
            return (int) (6 * scale); // Approximate height (half from original 12 to 6)
        }
    }

    /**
     * Forces a recheck of emoji renderer availability (for testing)
     */
    public static void recheckRenderers() {
        checkedRenderers = false;
        customEmojiAvailable = false;
        externalPngAvailable = false;
        generatedPngAvailable = false;

        // Reload custom emojis
        CustomEmojiLoader.reload();
    }

    /**
     * Gets the current rendering method being used
     */
    public static String getCurrentRenderingMethod() {
        checkRendererAvailability();

        if (customEmojiAvailable) {
            return "Custom Emojis (URL/File)";
        } else if (externalPngAvailable) {
            return "External PNG Files (" + ImprovedExternalPngRenderer.getLoadedTextureCount() + " loaded)";
        } else if (generatedPngAvailable) {
            return "Generated PNG Textures";
        } else {
            return "Unicode/Text Fallback";
        }
    }
}
