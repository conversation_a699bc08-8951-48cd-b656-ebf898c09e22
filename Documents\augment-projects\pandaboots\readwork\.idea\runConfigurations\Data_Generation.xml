<component name="ProjectRunConfigurationManager">
  <configuration default="false" factoryName="Application" name="Data Generation" type="Application">
    <option name="MAIN_CLASS_NAME" value="net.fabricmc.devlaunchinjector.Main"/>
    <module name="xdd.main"/>
    <option name="PROGRAM_PARAMETERS" value=""/>
    <option name="VM_PARAMETERS" value="-Dfabric.dli.config=C:\Users\<USER>\Desktop\xdd\.gradle\loom-cache\launch.cfg -Dfabric.dli.env=client -Dfabric-api.datagen -Dfabric-api.datagen.output-dir=C:\Users\<USER>\Desktop\xdd\src\main\generated -Dfabric.dli.main=net.fabricmc.loader.impl.launch.knot.KnotClient"/>
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/build/datagen/"/>
    <method v="2">
      <option enabled="true" name="Make"/>
    </method>
    <envs>
      
    </envs>
    <shortenClasspath name="ARGS_FILE"/>
  <classpathModifications/></configuration>
</component>