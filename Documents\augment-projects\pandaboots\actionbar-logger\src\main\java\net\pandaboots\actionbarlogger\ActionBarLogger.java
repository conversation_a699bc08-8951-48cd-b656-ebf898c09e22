package net.pandaboots.actionbarlogger;

import net.fabricmc.api.ClientModInitializer;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientLifecycleEvents;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.fabricmc.fabric.api.client.rendering.v1.HudRenderCallback;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;
import net.pandaboots.actionbarlogger.config.ActionBarConfig;
import net.pandaboots.actionbarlogger.gui.ActionBarSettingsScreen;
import net.pandaboots.actionbarlogger.logger.MessageLogger;
import net.pandaboots.actionbarlogger.render.StatusRenderer;
import org.lwjgl.glfw.GLFW;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ActionBarLogger implements ClientModInitializer {
    public static final String MOD_ID = "actionbar-logger";
    public static final Logger LOGGER = LoggerFactory.getLogger(MOD_ID);

    // Keybinding for opening settings
    private static KeyBinding settingsKeyBinding;

    @Override
    public void onInitializeClient() {
        LOGGER.info("Initializing Action Bar Logger v1.0.0...");

        // Initialize configuration
        ActionBarConfig.getInstance();

        // Initialize message logger
        MessageLogger.initialize();

        // Register keybinding for settings
        settingsKeyBinding = KeyBindingHelper.registerKeyBinding(new KeyBinding(
            "key.actionbar-logger.settings",
            InputUtil.Type.KEYSYM,
            GLFW.GLFW_KEY_R,
            "category.actionbar-logger"
        ));

        // Register HUD renderer for status display
        HudRenderCallback.EVENT.register((drawContext, tickDelta) -> {
            StatusRenderer.renderStatus(drawContext);
        });

        // Register tick events for mouse handling and keybinding
        ClientTickEvents.END_CLIENT_TICK.register(client -> {
            // Check for settings key press
            if (settingsKeyBinding.wasPressed()) {
                client.setScreen(new ActionBarSettingsScreen(client.currentScreen));
            }

            // Handle mouse input for dragging (only when no screen is open)
            if (client.currentScreen == null) {
                handleMouseInput(client);
            }
        });

        // Register shutdown hook to ensure proper cleanup
        ClientLifecycleEvents.CLIENT_STOPPING.register(client -> {
            LOGGER.info("Shutting down Action Bar Logger...");
            MessageLogger.shutdown();
            StatusRenderer.clearAllEffects();
        });

        LOGGER.info("Action Bar Logger v1.0.0 initialized successfully!");
    }

    /**
     * Handles mouse input for dragging the status box
     * @param client The Minecraft client
     */
    private static void handleMouseInput(MinecraftClient client) {
        if (client.mouse == null) return;

        double mouseX = client.mouse.getX() * client.getWindow().getScaledWidth() / client.getWindow().getWidth();
        double mouseY = client.mouse.getY() * client.getWindow().getScaledHeight() / client.getWindow().getHeight();

        // Check for mouse button state
        boolean leftPressed = GLFW.glfwGetMouseButton(client.getWindow().getHandle(), GLFW.GLFW_MOUSE_BUTTON_LEFT) == GLFW.GLFW_PRESS;

        if (leftPressed) {
            if (!StatusRenderer.isDragging()) {
                // Try to start dragging
                StatusRenderer.handleMouseClick(mouseX, mouseY);
            } else {
                // Continue dragging
                StatusRenderer.handleMouseDrag(mouseX, mouseY);
            }
        } else {
            // Mouse released
            if (StatusRenderer.isDragging()) {
                StatusRenderer.handleMouseRelease();
            }
        }
    }

    /**
     * Called by the mixin when an action bar message is received
     * @param message The action bar message text
     */
    public static void onActionBarMessage(String message) {
        if (message != null && !message.trim().isEmpty()) {
            MessageLogger.logMessage(message);
        }
    }
}
