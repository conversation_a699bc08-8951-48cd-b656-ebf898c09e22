package net.marko.runicmod.gui.modern;

import net.minecraft.client.gui.DrawContext;
import net.minecraft.text.Text;

/**
 * Modern styled button with smooth hover effects
 */
public class ModernButton extends ModernWidget {
    
    private final Runnable onPressAction;
    private final ButtonStyle style;
    
    public enum ButtonStyle {
        PRIMARY(ModernWidget.ACCENT_COLOR, ModernWidget.TEXT_COLOR),
        SECONDARY(ModernWidget.SECONDARY_COLOR, ModernWidget.TEXT_COLOR),
        SUCCESS(ModernWidget.SUCCESS_COLOR, ModernWidget.TEXT_COLOR),
        WARNING(ModernWidget.WARNING_COLOR, ModernWidget.TEXT_COLOR),
        ERROR(ModernWidget.ERROR_COLOR, ModernWidget.TEXT_COLOR);
        
        private final int color;
        private final int textColor;
        
        ButtonStyle(int color, int textColor) {
            this.color = color;
            this.textColor = textColor;
        }
        
        public int getColor() { return color; }
        public int getTextColor() { return textColor; }
    }
    
    public ModernButton(int x, int y, int width, int height, String text, Runnable onPress) {
        this(x, y, width, height, text, onPress, ButtonStyle.PRIMARY);
    }
    
    public ModernButton(int x, int y, int width, int height, String text, Runnable onPress, ButtonStyle style) {
        super(x, y, width, height, Text.literal(text));
        this.onPressAction = onPress;
        this.style = style;
    }
    
    @Override
    protected void renderContent(DrawContext context, int mouseX, int mouseY, float delta) {
        String text = getMessage().getString();
        int textColor = style.getTextColor();

        // Calculate available space for text (8px padding on each side)
        int maxTextWidth = getWidth() - 16;
        int maxTextHeight = getHeight() - 8; // 4px padding top/bottom

        // Use advanced text fitting with wrapping, scaling, and fallback truncation
        int textX = getX() + 8; // Left padding
        int textY = getY() + 4; // Top padding

        if (isPressed) {
            // Pressed state - text appears slightly lower with darker color
            textY += 1;
            textColor = darkenColor(textColor, 0.9f);
        }

        // Draw text shadow for depth (only in normal state)
        if (!isPressed) {
            drawFittedText(context, text, textX + 1, textY + 1, maxTextWidth, maxTextHeight, 0x40000000, TextAlignment.CENTER);
        }

        // Draw main text with advanced fitting
        drawFittedText(context, text, textX, textY, maxTextWidth, maxTextHeight, textColor, TextAlignment.CENTER);
    }
    
    @Override
    protected int getBackgroundColor() {
        switch (style) {
            case PRIMARY:
                return isPressed ? ACCENT_HOVER : (isHovered ? ACCENT_COLOR : 0xFF2563EB);
            case SUCCESS:
                return isPressed ? 0xFF059669 : (isHovered ? SUCCESS_COLOR : 0xFF10B981);
            case WARNING:
                return isPressed ? 0xFFD97706 : (isHovered ? WARNING_COLOR : 0xFFF59E0B);
            case ERROR:
                return isPressed ? 0xFFDC2626 : (isHovered ? ERROR_COLOR : 0xFFEF4444);
            case SECONDARY:
            default:
                return isPressed ? ACTIVE_COLOR : (isHovered ? HOVER_COLOR : SECONDARY_COLOR);
        }
    }
    
    @Override
    protected int getBorderColor() {
        if (isHovered) return lightenColor(style.getColor(), 1.2f);
        return style.getColor();
    }
    
    @Override
    protected void onPress() {
        if (onPressAction != null) {
            onPressAction.run();
        }
    }
    
    /**
     * Darkens a color by a factor
     */
    private int darkenColor(int color, float factor) {
        int r = (int) (((color >> 16) & 0xFF) * factor);
        int g = (int) (((color >> 8) & 0xFF) * factor);
        int b = (int) ((color & 0xFF) * factor);
        int a = (color >> 24) & 0xFF;
        
        return (a << 24) | (r << 16) | (g << 8) | b;
    }
    
    /**
     * Lightens a color by a factor
     */
    private int lightenColor(int color, float factor) {
        int r = Math.min(255, (int) (((color >> 16) & 0xFF) * factor));
        int g = Math.min(255, (int) (((color >> 8) & 0xFF) * factor));
        int b = Math.min(255, (int) ((color & 0xFF) * factor));
        int a = (color >> 24) & 0xFF;

        return (a << 24) | (r << 16) | (g << 8) | b;
    }

}
