package net.pandaboots.actionbarlogger.gui.modern;

import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.text.Text;

import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * Modern toggle switch with smooth animations
 * Adapted from readwork mod's ModernToggle
 */
public class ModernToggle extends ModernWidget {
    
    private final Supplier<Boolean> valueGetter;
    private final Consumer<Boolean> valueSetter;
    private final String label;
    
    private static final int TOGGLE_WIDTH = 40;
    private static final int TOGGLE_HEIGHT = 20;
    private static final int KNOB_SIZE = 16;
    private static final int KNOB_MARGIN = 2;
    
    public ModernToggle(int x, int y, int width, int height, String label, 
                       Supplier<Boolean> getter, Consumer<Boolean> setter) {
        super(x, y, width, height, Text.literal(label));
        this.label = label;
        this.valueGetter = getter;
        this.valueSetter = setter;
    }
    
    @Override
    protected void renderContent(DrawContext context, int mouseX, int mouseY, float delta) {
        boolean isOn = valueGetter.get();

        // Calculate toggle position first to determine available text space
        int toggleX = getX() + getWidth() - TOGGLE_WIDTH - 8;
        int toggleY = getY() + (getHeight() - TOGGLE_HEIGHT) / 2;

        // Draw label
        var textRenderer = MinecraftClient.getInstance().textRenderer;
        int textX = getX() + 8;
        int textY = getY() + (getHeight() - textRenderer.fontHeight) / 2;
        context.drawText(textRenderer, label, textX, textY, getTextColor(), false);
        
        // Draw toggle background
        int toggleBgColor = isOn ? SUCCESS_COLOR : SECONDARY_COLOR;
        if (isHovered) {
            toggleBgColor = lightenColor(toggleBgColor, 1.1f);
        }
        
        // Rounded rectangle for toggle background
        context.fill(toggleX, toggleY, toggleX + TOGGLE_WIDTH, toggleY + TOGGLE_HEIGHT, toggleBgColor);
        
        // Draw toggle knob
        int knobX = isOn ? 
            toggleX + TOGGLE_WIDTH - KNOB_SIZE - KNOB_MARGIN : 
            toggleX + KNOB_MARGIN;
        int knobY = toggleY + KNOB_MARGIN;
        
        int knobColor = TEXT_COLOR;
        context.fill(knobX, knobY, knobX + KNOB_SIZE, knobY + KNOB_SIZE, knobColor);
        
        // Draw toggle border
        context.drawBorder(toggleX, toggleY, TOGGLE_WIDTH, TOGGLE_HEIGHT, getBorderColor());
    }
    
    @Override
    protected void renderBackground(DrawContext context, int mouseX, int mouseY, float delta) {
        // Custom background for toggle
        int backgroundColor = isHovered ? HOVER_COLOR : PRIMARY_COLOR;
        context.fill(getX(), getY(), getX() + getWidth(), getY() + getHeight(), backgroundColor);
    }
    
    @Override
    protected void onPress() {
        boolean currentValue = valueGetter.get();
        valueSetter.accept(!currentValue);
    }
}
