<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="LoomLocalRemappedMods" />
      <option name="name" value="LoomLocalRemappedMods" />
      <option name="url" value="file:/$PROJECT_DIR$/.gradle/loom-cache/remapped_mods/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="LoomGlobalMinecraft" />
      <option name="name" value="LoomGlobalMinecraft" />
      <option name="url" value="file:/$USER_HOME$/.gradle/caches/fabric-loom/minecraftMaven/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="LoomLocalMinecraft" />
      <option name="name" value="LoomLocalMinecraft" />
      <option name="url" value="file:/$PROJECT_DIR$/.gradle/loom-cache/minecraftMaven/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="Fabric" />
      <option name="name" value="Fabric" />
      <option name="url" value="https://maven.fabricmc.net/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="Mojang" />
      <option name="name" value="Mojang" />
      <option name="url" value="https://libraries.minecraft.net/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="MavenRepo" />
      <option name="name" value="MavenRepo" />
      <option name="url" value="https://repo.maven.apache.org/maven2/" />
    </remote-repository>
  </component>
</project>