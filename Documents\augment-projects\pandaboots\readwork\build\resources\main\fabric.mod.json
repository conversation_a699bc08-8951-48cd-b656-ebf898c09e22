{"schemaVersion": 1, "id": "rune-utils", "version": "2.0.0", "name": "<PERSON><PERSON>", "description": "A utility mod for detecting and displaying rune effects including Rune Obstruction, Sky Stepper, and Dash<PERSON> with modern GUI and customizable settings", "authors": ["Cerv"], "contact": {"homepage": "https://fabricmc.net/", "sources": "https://github.com/FabricMC/fabric-example-mod"}, "license": "CC0-1.0", "icon": "assets/runic-mod/icon.png", "environment": "client", "entrypoints": {"client": ["net.marko.runicmod.RunicMod"]}, "mixins": ["runic-mod.mixins.json"], "depends": {"fabricloader": ">=0.16.12", "minecraft": "~1.21.1", "java": ">=21", "fabric-api": "*"}, "suggests": {}}