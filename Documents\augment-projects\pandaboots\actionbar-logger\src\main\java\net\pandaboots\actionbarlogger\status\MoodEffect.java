package net.pandaboots.actionbarlogger.status;

/**
 * Represents an active mood effect with timing information
 */
public class MoodEffect {
    private final String id;
    private final MoodStatus status;
    private final int tier;
    private final int originalDuration;
    private final long startTime;
    private final long endTime;

    public MoodEffect(String id, MoodStatus status, int tier, int duration) {
        this.id = id;
        this.status = status;
        this.tier = Math.max(1, Math.min(5, tier)); // Clamp tier to 1-5
        this.originalDuration = duration;
        this.startTime = System.currentTimeMillis();
        this.endTime = startTime + (duration * 1000L);
    }

    public String getId() {
        return id;
    }

    public MoodStatus getStatus() {
        return status;
    }

    public int getTier() {
        return tier;
    }

    public int getOriginalDuration() {
        return originalDuration;
    }

    public long getStartTime() {
        return startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    /**
     * Gets the remaining time in seconds
     * @return Remaining seconds, or 0 if expired
     */
    public int getRemainingSeconds() {
        long remaining = endTime - System.currentTimeMillis();
        return Math.max(0, (int) (remaining / 1000) + 1);
    }

    /**
     * Checks if this effect has expired
     * @return true if expired, false otherwise
     */
    public boolean isExpired() {
        return System.currentTimeMillis() > endTime;
    }

    /**
     * Gets the display text for this effect
     * @return Formatted display text
     */
    public String getDisplayText() {
        return status.getDisplayText(tier, getRemainingSeconds());
    }

    /**
     * Gets the effect description text
     * @return Effect description
     */
    public String getEffectDescription() {
        return status.getEffectDescription(tier);
    }

    /**
     * Gets the progress of this effect (0.0 to 1.0)
     * @return Progress value
     */
    public float getProgress() {
        long elapsed = System.currentTimeMillis() - startTime;
        long total = endTime - startTime;
        return Math.min(1.0f, (float) elapsed / total);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        MoodEffect that = (MoodEffect) obj;
        return id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }
}
