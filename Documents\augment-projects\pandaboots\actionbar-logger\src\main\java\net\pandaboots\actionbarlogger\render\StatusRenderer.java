package net.pandaboots.actionbarlogger.render;

import net.minecraft.client.MinecraftClient;
import net.minecraft.client.font.TextRenderer;
import net.minecraft.client.gui.DrawContext;
import net.pandaboots.actionbarlogger.ActionBarLogger;
import net.pandaboots.actionbarlogger.config.ActionBarConfig;
import net.pandaboots.actionbarlogger.status.MoodEffect;
import net.pandaboots.actionbarlogger.status.MoodStatus;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Renders the mood status display box inspired by readwork's CustomTextRenderer
 */
public class StatusRenderer {
    private static MoodEffect currentEffect = null;
    private static final int PADDING = 12;
    private static final int LINE_HEIGHT = 16;
    private static final int BORDER_WIDTH = 2;
    
    // Colors inspired by readwork's ModernWidget
    private static final int BACKGROUND_COLOR = 0xE0000000; // Semi-transparent black
    private static final int BORDER_COLOR = 0xFF404040;     // Dark gray
    private static final int TITLE_COLOR = 0xFFFFFFFF;      // White
    private static final int SHADOW_COLOR = 0x80000000;     // Semi-transparent black
    
    // Dragging state
    private static boolean isDragging = false;
    private static double dragStartX = 0;
    private static double dragStartY = 0;
    private static int dragStartBoxX = 0;
    private static int dragStartBoxY = 0;

    /**
     * Adds a new mood effect (replaces any existing effect - "latest wins" logic)
     * @param status The mood status
     * @param tier The tier level (1-5)
     * @param duration The duration in seconds (default 10)
     */
    public static void addMoodEffect(MoodStatus status, int tier, int duration) {
        // Create unique ID for this effect
        String id = status.name() + "_" + System.currentTimeMillis();

        // Replace any existing effect with new one (latest wins)
        currentEffect = new MoodEffect(id, status, tier, duration);

        ActionBarLogger.LOGGER.info("Added mood effect: {} {}, duration: {} (replaced existing)",
            status.getDisplayName(), tier, duration);
    }

    /**
     * Renders the status display
     * @param context The draw context
     */
    public static void renderStatus(DrawContext context) {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player == null) return;

        ActionBarConfig config = ActionBarConfig.getInstance();
        if (!config.isShowStatusDisplay()) return;

        // Remove expired effect
        if (currentEffect != null && currentEffect.isExpired()) {
            currentEffect = null;
        }

        // Only render if we have an active effect
        if (currentEffect == null) return;

        renderStatusBox(context, config);
    }

    /**
     * Renders the status box with modern styling
     * @param context The draw context
     * @param config The configuration
     */
    private static void renderStatusBox(DrawContext context, ActionBarConfig config) {
        MinecraftClient client = MinecraftClient.getInstance();
        TextRenderer textRenderer = client.textRenderer;
        
        int screenWidth = client.getWindow().getScaledWidth();
        int screenHeight = client.getWindow().getScaledHeight();
        
        float scale = config.getStatusScale();
        
        // Calculate box dimensions
        String title = config.isShowMoodIcons() ? "🐼 Panda Boots Status" : "Panda Boots Status";
        int titleWidth = textRenderer.getWidth(title);
        int maxContentWidth = titleWidth;

        // Calculate lines needed for single effect
        int linesPerEffect = 1; // Status line
        if (config.isShowProgressBar()) linesPerEffect++;
        if (config.isShowEffectDescriptions()) linesPerEffect++;

        // Find the widest content line for the single effect
        String effectDisplayText = currentEffect.getDisplayText();
        int statusWidth = textRenderer.getWidth(effectDisplayText);
        maxContentWidth = Math.max(maxContentWidth, statusWidth);

        if (config.isShowEffectDescriptions()) {
            String descText = currentEffect.getEffectDescription();
            int descWidth = textRenderer.getWidth(descText);
            maxContentWidth = Math.max(maxContentWidth, descWidth);
        }

        int totalLines = 1 + linesPerEffect; // Title + single effect lines
        int boxWidth = (int) ((maxContentWidth + PADDING * 2) * scale);
        int boxHeight = (int) ((LINE_HEIGHT * totalLines + PADDING * 2) * scale);
        
        // Get box position
        int boxX = config.getStatusX();
        int boxY = config.getStatusY();
        
        // Center horizontally if x is -1
        if (boxX == -1) {
            boxX = (screenWidth / 2) - (boxWidth / 2);
        }
        
        // Ensure box stays on screen
        boxX = Math.max(0, Math.min(boxX, screenWidth - boxWidth));
        boxY = Math.max(0, Math.min(boxY, screenHeight - boxHeight));
        
        // Calculate dynamic colors based on configuration
        int shadowColor = (config.getShadowIntensity() << 24) | 0x000000; // Shadow with configurable intensity
        int backgroundColor = (config.getBackgroundOpacity() * 255 / 100 << 24) | config.getBackgroundColorInt();
        int borderColor = 0xFF000000 | config.getBorderColorInt();

        // Draw shadow for depth if enabled
        if (config.getShadowIntensity() > 0) {
            int shadowOffset = Math.max(1, config.getShadowIntensity() / 25);
            context.fill(boxX + shadowOffset, boxY + shadowOffset,
                        boxX + boxWidth + shadowOffset, boxY + boxHeight + shadowOffset, shadowColor);
        }

        // Draw background
        context.fill(boxX, boxY, boxX + boxWidth, boxY + boxHeight, backgroundColor);

        // Draw border if enabled
        if (config.getBorderThickness() > 0) {
            for (int i = 0; i < config.getBorderThickness(); i++) {
                context.drawBorder(boxX - i, boxY - i, boxWidth + (i * 2), boxHeight + (i * 2), borderColor);
            }
        }
        
        // Draw content with scaling
        context.getMatrices().push();
        context.getMatrices().scale(scale, scale, 1.0f);
        
        int scaledX = (int) (boxX / scale) + PADDING;
        int scaledY = (int) (boxY / scale) + PADDING;
        
        // Draw title
        context.drawText(textRenderer, title, scaledX, scaledY, config.getTextColorInt(), false);
        scaledY += LINE_HEIGHT;

        // Draw single active effect
        // Draw status text
        String statusText = currentEffect.getDisplayText();
        int statusColor = getEffectColor(currentEffect, config);
        context.drawText(textRenderer, statusText, scaledX, scaledY, statusColor, false);
        scaledY += LINE_HEIGHT;

        // Draw progress bar if enabled
        if (config.isShowProgressBar()) {
            drawProgressBar(context, scaledX, scaledY, (int)((maxContentWidth) * scale), currentEffect, config);
            scaledY += LINE_HEIGHT;
        }

        // Draw effect description if enabled
        if (config.isShowEffectDescriptions()) {
            String descText = currentEffect.getEffectDescription();
            int descColor = 0xFF888888; // Muted gray
            context.drawText(textRenderer, descText, scaledX, scaledY, descColor, false);
            scaledY += LINE_HEIGHT;
        }
        
        context.getMatrices().pop();
    }

    /**
     * Handles mouse click for dragging
     * @param mouseX Mouse X position
     * @param mouseY Mouse Y position
     * @return true if the click was handled
     */
    public static boolean handleMouseClick(double mouseX, double mouseY) {
        ActionBarConfig config = ActionBarConfig.getInstance();
        if (!config.isDraggable() || currentEffect == null) return false;
        
        int[] bounds = getBoxBounds();
        if (bounds == null) return false;
        
        int boxX = bounds[0];
        int boxY = bounds[1];
        int boxWidth = bounds[2];
        int boxHeight = bounds[3];
        
        // Check if click is within the box
        if (mouseX >= boxX && mouseX <= boxX + boxWidth && 
            mouseY >= boxY && mouseY <= boxY + boxHeight) {
            
            isDragging = true;
            dragStartX = mouseX;
            dragStartY = mouseY;
            dragStartBoxX = config.getStatusX();
            dragStartBoxY = config.getStatusY();
            
            return true;
        }
        
        return false;
    }

    /**
     * Handles mouse drag
     * @param mouseX Mouse X position
     * @param mouseY Mouse Y position
     */
    public static void handleMouseDrag(double mouseX, double mouseY) {
        if (!isDragging) return;
        
        ActionBarConfig config = ActionBarConfig.getInstance();
        
        double deltaX = mouseX - dragStartX;
        double deltaY = mouseY - dragStartY;
        
        int newX = dragStartBoxX + (int) deltaX;
        int newY = dragStartBoxY + (int) deltaY;
        
        config.setStatusX(newX);
        config.setStatusY(newY);
    }

    /**
     * Handles mouse release
     */
    public static void handleMouseRelease() {
        isDragging = false;
    }

    /**
     * Gets the current box bounds for dragging
     * @return int array [x, y, width, height] or null if no effects
     */
    public static int[] getBoxBounds() {
        if (currentEffect == null) return null;
        
        ActionBarConfig config = ActionBarConfig.getInstance();
        MinecraftClient client = MinecraftClient.getInstance();
        TextRenderer textRenderer = client.textRenderer;
        
        int screenWidth = client.getWindow().getScaledWidth();
        float scale = config.getStatusScale();
        
        // Calculate box dimensions (same as renderStatusBox)
        String title = "Panda Boots Status";
        int titleWidth = textRenderer.getWidth(title);
        int maxContentWidth = titleWidth;
        
        // Calculate for single effect
        String text = currentEffect.getDisplayText();
        int textWidth = textRenderer.getWidth(text);
        maxContentWidth = Math.max(maxContentWidth, textWidth);

        if (config.isShowEffectDescriptions()) {
            String descText = currentEffect.getEffectDescription();
            int descWidth = textRenderer.getWidth(descText);
            maxContentWidth = Math.max(maxContentWidth, descWidth);
        }
        
        int boxWidth = (int) ((maxContentWidth + PADDING * 2) * scale);
        // Calculate height for single effect
        int linesForEffect = 1; // Status line
        if (config.isShowProgressBar()) linesForEffect++;
        if (config.isShowEffectDescriptions()) linesForEffect++;

        int boxHeight = (int) ((LINE_HEIGHT * (1 + linesForEffect) + PADDING * 2) * scale);
        
        int boxX = config.getStatusX();
        int boxY = config.getStatusY();
        
        if (boxX == -1) {
            boxX = (screenWidth / 2) - (boxWidth / 2);
        }
        
        return new int[]{boxX, boxY, boxWidth, boxHeight};
    }

    /**
     * Checks if currently dragging
     * @return true if dragging, false otherwise
     */
    public static boolean isDragging() {
        return isDragging;
    }

    /**
     * Gets the color for an effect based on configuration
     * @param effect The mood effect
     * @param config The configuration
     * @return Color integer
     */
    private static int getEffectColor(MoodEffect effect, ActionBarConfig config) {
        switch (effect.getStatus()) {
            case LAZY:
                return config.getLazyColorInt();
            case AGGRESSIVE:
                return config.getAggressiveColorInt();
            case PLAYFUL:
                return config.getPlayfulColorInt();
            default:
                return config.getTextColorInt();
        }
    }

    /**
     * Draws a progress bar for the effect
     * @param context The draw context
     * @param x X position
     * @param y Y position
     * @param width Bar width
     * @param effect The mood effect
     * @param config The configuration
     */
    private static void drawProgressBar(DrawContext context, int x, int y, int width, MoodEffect effect, ActionBarConfig config) {
        int barHeight = 4;
        int barY = y + 6; // Offset from text

        // Background bar
        context.fill(x, barY, x + width, barY + barHeight, 0xFF333333);

        // Progress bar - calculate remaining time percentage
        int remainingSeconds = effect.getRemainingSeconds();
        int totalSeconds = effect.getOriginalDuration();
        float progress = (float) remainingSeconds / totalSeconds; // 1.0 = full time, 0.0 = expired
        int progressWidth = (int) (width * progress);
        int progressColor = getEffectColor(effect, config);

        if (progressWidth > 0) {
            context.fill(x, barY, x + progressWidth, barY + barHeight, progressColor);
        }

        // Progress percentage text
        int percentage = (int) (progress * 100);
        String progressText = percentage + "%";
        int textX = x + width - 30; // Right-aligned
        context.drawText(MinecraftClient.getInstance().textRenderer, progressText, textX, y, 0xFFAAAAAA, false);
    }

    /**
     * Clears the current active effect
     */
    public static void clearAllEffects() {
        currentEffect = null;
    }
}
