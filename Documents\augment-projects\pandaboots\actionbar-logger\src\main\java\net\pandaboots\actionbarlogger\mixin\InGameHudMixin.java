package net.pandaboots.actionbarlogger.mixin;

import net.minecraft.client.gui.hud.InGameHud;
import net.minecraft.text.Text;
import net.pandaboots.actionbarlogger.ActionBarLogger;
import net.pandaboots.actionbarlogger.render.StatusRenderer;
import net.pandaboots.actionbarlogger.status.MoodStatus;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(InGameHud.class)
public class InGameHudMixin {

    /**
     * Intercepts action bar messages when they are set
     * @param message The action bar message text
     * @param tinted Whether the message should be tinted (unused for logging)
     * @param ci Callback info
     */
    @Inject(method = "setOverlayMessage", at = @At("HEAD"))
    private void onSetOverlayMessage(Text message, boolean tinted, CallbackInfo ci) {
        if (message != null) {
            String messageText = message.getString();
            if (messageText != null && !messageText.trim().isEmpty()) {
                // Log the message
                ActionBarLogger.onActionBarMessage(messageText);

                // Check for mood swing messages and update status display
                if (MoodStatus.isMoodSwingMessage(messageText)) {
                    MoodStatus.MoodParseResult result = MoodStatus.parseMessage(messageText);
                    if (result != null) {
                        // Each mood swing lasts 10 seconds
                        StatusRenderer.addMoodEffect(result.getStatus(), result.getTier(), 10);
                    }
                }
            }
        }
    }
}
