package net.marko.runicmod;

import net.marko.runicmod.config.RunicModConfig;
import net.marko.runicmod.render.CustomTextRenderer;
import net.minecraft.client.MinecraftClient;
import org.slf4j.Logger;

/**
 * Manages title displays for runic obstructions
 */
public class TitleManager {
    private static final Logger LOGGER = RunicMod.LOGGER;

    /**
     * Shows a runic title for a player
     * @param playerName The name of the player affected by runic obstruction
     * @param durationStr The duration of the runic obstruction in seconds
     * @deprecated Use CustomTextRenderer.addRuneEffect directly
     */
    @Deprecated
    public static void showRunicTitle(String playerName, String durationStr) {
        // This method is kept for compatibility but is no longer used
        // The new system handles display through CustomTextRenderer.addRuneEffect
        LOGGER.debug("Legacy showRunicTitle called for player: {} (deprecated)", playerName);
    }

    /**
     * Cleans up resources when the mod is unloaded
     */
    public static void shutdown() {
        // Clear all effects
        CustomTextRenderer.clearAllEffects();

        LOGGER.info("TitleManager shutdown complete");
    }
}
