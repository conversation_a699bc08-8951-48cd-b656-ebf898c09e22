package net.marko.runicmod.rune;

/**
 * Enum representing different types of runes that can be detected
 */
public enum RuneType {
    RUNIC_OBSTRUCTION("Rune Obstruction", "RUNED", 6),
    SKY_STEPPER("Sky Stepper", "STEPPED", 60),
    <PERSON><PERSON><PERSON><PERSON>("Dasher", "DASHED", 60);

    private final String displayName;
    private final String actionText;
    private final int maxDuration;

    RuneType(String displayName, String actionText, int maxDuration) {
        this.displayName = displayName;
        this.actionText = actionText;
        this.maxDuration = maxDuration;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getActionText() {
        return actionText;
    }

    public int getMaxDuration() {
        return maxDuration;
    }

    /**
     * Gets the display text for a rune effect
     * @param playerName The affected player name
     * @param duration The duration in seconds
     * @return The formatted display text
     */
    public String getDisplayText(String playerName, int duration) {
        switch (this) {
            case RUNIC_OBSTRUCTION:
                return playerName + " IS " + actionText + " FOR " + duration;
            case SKY_STEPPER:
            case DASHER:
                return displayName + " COOLDOWN: " + duration + "s";
            default:
                return displayName + ": " + duration + "s";
        }
    }

    /**
     * Gets the color for this rune type
     * @return RGB color value
     */
    public int getColor() {
        switch (this) {
            case RUNIC_OBSTRUCTION:
                return 0xFF0000; // Red
            case SKY_STEPPER:
                return 0x00FFFF; // Cyan
            case DASHER:
                return 0xFFFF00; // Yellow
            default:
                return 0xFFFFFF; // White
        }
    }
}
