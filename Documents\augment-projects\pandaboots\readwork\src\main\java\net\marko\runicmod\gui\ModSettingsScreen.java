package net.marko.runicmod.gui;

import net.marko.runicmod.RunicMod;
import net.marko.runicmod.config.RunicModConfig;
import net.marko.runicmod.gui.modern.*;
import net.marko.runicmod.render.CustomTextRenderer;
import net.marko.runicmod.rune.RuneType;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.text.Text;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * Modern settings screen for Rune Utils with overhauled UI
 */
public class ModSettingsScreen extends Screen {
    private static final Logger LOGGER = RunicMod.LOGGER;

    private final Screen parent;
    private final RunicModConfig config;
    private ModernTabContainer tabContainer;
    private final List<ModernWidget> widgets = new ArrayList<>();

    // UI Layout constants - Responsive GUI sizing with 16:9 aspect ratio
    private static final int BASE_GUI_WIDTH = 480;   // 16:9 ratio base size
    private static final int BASE_GUI_HEIGHT = 270;  // 480 * 9/16 = 270
    private static final int LARGE_GUI_WIDTH = 960;  // 16:9 ratio large size
    private static final int LARGE_GUI_HEIGHT = 540; // 960 * 9/16 = 540
    private static final int MIN_SCREEN_WIDTH = 800;
    private static final int MIN_SCREEN_HEIGHT = 600;
    private static final float MAX_SCREEN_RATIO = 0.9f;
    private static final float ASPECT_RATIO = 16.0f / 9.0f; // 16:9 aspect ratio

    // Compact spacing constants for efficient space usage with improved text handling
    private static final int MARGIN = 16;                    // Keep outer margin for boundaries
    private static final int WIDGET_HEIGHT = 28;             // Increased from 24px for better text fit
    private static final int WIDGET_SPACING = 10;            // Increased spacing between widgets for clarity
    private static final int SECTION_SPACING = 16;           // Increased spacing between major sections
    private static final int BUTTON_WIDTH = 120;
    private static final int BUTTON_HEIGHT = 28;             // Match widget height
    private static final int SLIDER_WIDTH = 200;
    private static final int COLUMN_SPACING = 16;            // Increased spacing between columns for text clearance
    private static final int TEXT_PADDING = 8;               // Increased padding around text elements

    // Dynamic GUI dimensions
    private int guiWidth;
    private int guiHeight;

    // Scrollable containers for tabs that need scrolling
    private net.marko.runicmod.gui.modern.ScrollableContainer audioScrollContainer;
    private net.marko.runicmod.gui.modern.ScrollableContainer generalScrollContainer;
    
    public ModSettingsScreen(Screen parent) {
        super(Text.literal("Rune Utils Settings"));
        this.parent = parent;
        this.config = RunicModConfig.getInstance();
    }
    
    @Override
    protected void init() {
        super.init();
        widgets.clear();

        try {
            // Calculate responsive GUI dimensions
            calculateGuiDimensions();

            // Calculate centered position
            int guiX = (width - guiWidth) / 2;
            int guiY = (height - guiHeight) / 2;

            // Initialize tab container with responsive sizing
            int containerWidth = guiWidth - MARGIN * 2;
            int containerHeight = guiHeight - MARGIN * 2 - 40; // Leave space for bottom buttons

            tabContainer = new ModernTabContainer(guiX + MARGIN, guiY + MARGIN, containerWidth, containerHeight);

            // Add tabs with icons
            tabContainer.addTab("Display", "🎨", this::initDisplayTab);
            tabContainer.addTab("Audio", "🔊", this::initAudioTab);
            tabContainer.addTab("Position", "📍", this::initPositionTab);
            tabContainer.addTab("General", "⚙️", this::initGeneralTab);

            // Initialize first tab
            initDisplayTab();

        } catch (Exception e) {
            LOGGER.error("Error initializing modern settings screen", e);
        }

        // Add bottom action buttons
        initBottomButtons();
    }

    /**
     * Calculates responsive GUI dimensions based on screen size while maintaining 16:9 aspect ratio
     */
    private void calculateGuiDimensions() {
        // Check if screen is large enough for large GUI
        boolean useLargeGui = width >= MIN_SCREEN_WIDTH && height >= MIN_SCREEN_HEIGHT;

        // Calculate maximum allowed dimensions
        int maxWidth = (int) (width * MAX_SCREEN_RATIO);
        int maxHeight = (int) (height * MAX_SCREEN_RATIO);

        if (useLargeGui) {
            // Start with large GUI dimensions
            guiWidth = LARGE_GUI_WIDTH;
            guiHeight = LARGE_GUI_HEIGHT;
        } else {
            // Start with base GUI dimensions
            guiWidth = BASE_GUI_WIDTH;
            guiHeight = BASE_GUI_HEIGHT;
        }

        // Ensure GUI fits within screen bounds while maintaining 16:9 aspect ratio
        if (guiWidth > maxWidth) {
            guiWidth = maxWidth;
            guiHeight = (int) (guiWidth / ASPECT_RATIO);
        }

        if (guiHeight > maxHeight) {
            guiHeight = maxHeight;
            guiWidth = (int) (guiHeight * ASPECT_RATIO);
        }

        // Ensure minimum usable size while maintaining aspect ratio
        int minWidth = 400;  // Minimum width for 16:9
        int minHeight = (int) (minWidth / ASPECT_RATIO); // 225 for 16:9

        if (guiWidth < minWidth) {
            guiWidth = minWidth;
            guiHeight = minHeight;
        }

        // Final check to ensure we maintain 16:9 ratio
        if (Math.abs((float) guiWidth / guiHeight - ASPECT_RATIO) > 0.01f) {
            // Adjust height to match width for perfect 16:9
            guiHeight = (int) (guiWidth / ASPECT_RATIO);
        }
    }

    private void initBottomButtons() {
        // Calculate centered position for responsive GUI
        int guiX = (width - guiWidth) / 2;
        int guiY = (height - guiHeight) / 2;
        int buttonY = guiY + guiHeight - 35;
        int buttonSpacing = 10;

        // Save & Close button
        ModernButton saveButton = new ModernButton(
            guiX + MARGIN, buttonY,
            BUTTON_WIDTH, BUTTON_HEIGHT, "💾 Save & Close",
            () -> {
                try {
                    config.saveConfig();
                    LOGGER.info("Settings saved successfully");
                    close();
                } catch (Exception e) {
                    LOGGER.error("Error saving config", e);
                }
            }, ModernButton.ButtonStyle.SUCCESS
        );
        addDrawableChild(saveButton);

        // Apply button
        ModernButton applyButton = new ModernButton(
            guiX + MARGIN + BUTTON_WIDTH + buttonSpacing, buttonY,
            BUTTON_WIDTH, BUTTON_HEIGHT, "✓ Apply",
            () -> {
                try {
                    config.saveConfig();
                    LOGGER.info("Settings applied");
                } catch (Exception e) {
                    LOGGER.error("Error applying config", e);
                }
            }, ModernButton.ButtonStyle.PRIMARY
        );
        addDrawableChild(applyButton);

        // Cancel button
        ModernButton cancelButton = new ModernButton(
            guiX + guiWidth - MARGIN - BUTTON_WIDTH, buttonY,
            BUTTON_WIDTH, BUTTON_HEIGHT, "✕ Cancel",
            this::close, ModernButton.ButtonStyle.SECONDARY
        );
        addDrawableChild(cancelButton);
    }
    
    private void initDisplayTab() {
        widgets.clear();

        int contentX = tabContainer.getContentX() + MARGIN;
        int contentY = tabContainer.getContentY() + MARGIN;
        int contentWidth = tabContainer.getContentWidth() - MARGIN * 2;
        int currentY = contentY;

        // Three-column layout with improved spacing for 16:9 interface
        int columnWidth = (contentWidth - COLUMN_SPACING * 2) / 3;
        int column2X = contentX + columnWidth + COLUMN_SPACING;
        int column3X = contentX + (columnWidth + COLUMN_SPACING) * 2;

        // Section title
        drawSectionTitle("Display Settings");

        // Top row - Three controls with better spacing
        // Column 1 - Show Title Toggle
        ModernToggle showTitleToggle = new ModernToggle(
            contentX, currentY, columnWidth, WIDGET_HEIGHT,
            "📺 Show Rune Effects",
            config::isShowTitle, config::setShowTitle
        );
        widgets.add(showTitleToggle);

        // Column 2 - Test Display Button
        ModernButton testButton = new ModernButton(
            column2X, currentY, columnWidth, WIDGET_HEIGHT,
            "🧪 Test Display",
            () -> {
                CustomTextRenderer.addRuneEffect(RuneType.RUNIC_OBSTRUCTION, "TestPlayer", 5, "III");
                CustomTextRenderer.addRuneEffect(RuneType.SKY_STEPPER, null, 10, "V");
                CustomTextRenderer.addRuneEffect(RuneType.DASHER, null, 8, "II");
            }, ModernButton.ButtonStyle.WARNING
        );
        widgets.add(testButton);

        // Column 3 - Clear Effects Button
        ModernButton clearButton = new ModernButton(
            column3X, currentY, columnWidth, WIDGET_HEIGHT,
            "🗑️ Clear All Effects",
            CustomTextRenderer::clearAllEffects, ModernButton.ButtonStyle.ERROR
        );
        widgets.add(clearButton);
        currentY += WIDGET_HEIGHT + SECTION_SPACING;

        // Full-width Text Scale Slider with better spacing
        ModernSlider scaleSlider = new ModernSlider(
            contentX, currentY, contentWidth, WIDGET_HEIGHT + 10,
            "Text Scale", 0.5f, 2.0f,
            config::getTitleScale, config::setTitleScale,
            value -> String.format("%.0f%%", value * 100)
        );
        widgets.add(scaleSlider);
    }

    /**
     * Helper method to draw section titles (placeholder for future implementation)
     */
    private void drawSectionTitle(String title) {
        // This will be implemented when we add section headers
    }
    
    private void initAudioTab() {
        widgets.clear();

        int contentX = tabContainer.getContentX() + MARGIN;
        int contentY = tabContainer.getContentY() + MARGIN;
        int contentWidth = tabContainer.getContentWidth() - MARGIN * 2;
        int contentHeight = tabContainer.getContentHeight() - MARGIN * 2;

        // Create scrollable container for audio content
        net.marko.runicmod.gui.modern.ScrollableContainer scrollContainer =
            new net.marko.runicmod.gui.modern.ScrollableContainer(contentX, contentY, contentWidth, contentHeight);

        int currentY = contentY;

        // Master controls at the top with compact spacing
        int masterControlWidth = (contentWidth - COLUMN_SPACING) / 2;

        // Master Enable Toggle
        ModernToggle masterSoundsToggle = new ModernToggle(
            contentX, currentY, masterControlWidth, WIDGET_HEIGHT,
            "🔊 Master Audio Enable",
            config::isPlaySounds, config::setPlaySounds
        );
        scrollContainer.addChild(masterSoundsToggle);

        // Master Volume Slider
        ModernSlider masterVolumeSlider = new ModernSlider(
            contentX + masterControlWidth + COLUMN_SPACING, currentY, masterControlWidth, WIDGET_HEIGHT,
            "Master Volume", 0.0f, 1.0f,
            config::getSoundVolume, config::setSoundVolume,
            value -> String.format("%.0f%%", value * 100)
        );
        scrollContainer.addChild(masterVolumeSlider);
        currentY += WIDGET_HEIGHT + SECTION_SPACING;

        // Individual rune controls - Three columns with improved spacing for text clarity
        // Ensure minimum column width for proper text display
        int minColumnWidth = 140; // Minimum width to prevent text overlap
        int availableWidth = contentWidth - COLUMN_SPACING * 2;
        int calculatedColumnWidth = availableWidth / 3;
        int columnWidth = Math.max(minColumnWidth, calculatedColumnWidth);

        // If columns would be too wide, reduce spacing slightly
        if (columnWidth * 3 + COLUMN_SPACING * 2 > contentWidth) {
            columnWidth = (contentWidth - 10) / 3; // Reduce spacing to 5px each side
        }

        // Add all rune audio columns to scrollable container
        initRuneAudioColumnScrollable(scrollContainer, contentX, currentY, columnWidth, "🔴 Rune Obstruction",
                           config::isEnableRunicObstructionSound, config::setEnableRunicObstructionSound,
                           config::getRunicObstructionVolume, config::setRunicObstructionVolume,
                           config::getRunicObstructionSoundId, config::setRunicObstructionSoundId,
                           RuneType.RUNIC_OBSTRUCTION);

        initRuneAudioColumnScrollable(scrollContainer, contentX + columnWidth + COLUMN_SPACING, currentY, columnWidth, "🔵 Sky Stepper",
                           config::isEnableSkyStepperSound, config::setEnableSkyStepperSound,
                           config::getSkyStepperVolume, config::setSkyStepperVolume,
                           config::getSkyStepperSoundId, config::setSkyStepperSoundId,
                           RuneType.SKY_STEPPER);

        initRuneAudioColumnScrollable(scrollContainer, contentX + (columnWidth + COLUMN_SPACING) * 2, currentY, columnWidth, "🟡 Dasher",
                           config::isEnableDasherSound, config::setEnableDasherSound,
                           config::getDasherVolume, config::setDasherVolume,
                           config::getDasherSoundId, config::setDasherSoundId,
                           RuneType.DASHER);

        // Add the scrollable container to the main widgets list
        // Note: We'll need to handle this differently since ScrollableContainer isn't a ModernWidget
        // For now, we'll store it separately and handle rendering manually
        audioScrollContainer = scrollContainer;
    }

    /**
     * Creates individual audio controls for a specific rune type with improved spacing
     */
    private void initRuneAudioColumn(int x, int y, int width, String title,
                                   java.util.function.Supplier<Boolean> enableGetter,
                                   java.util.function.Consumer<Boolean> enableSetter,
                                   java.util.function.Supplier<Float> volumeGetter,
                                   java.util.function.Consumer<Float> volumeSetter,
                                   java.util.function.Supplier<String> soundGetter,
                                   java.util.function.Consumer<String> soundSetter,
                                   RuneType runeType) {
        int currentY = y;

        // Enable toggle for this rune type with better text sizing
        ModernToggle enableToggle = new ModernToggle(
            x, currentY, width, WIDGET_HEIGHT,
            title,
            enableGetter, enableSetter
        );
        widgets.add(enableToggle);
        currentY += WIDGET_HEIGHT + WIDGET_SPACING;

        // Volume slider for this rune type with proper spacing
        ModernSlider volumeSlider = new ModernSlider(
            x, currentY, width, WIDGET_HEIGHT + 8,
            "Volume", 0.0f, 1.0f,
            volumeGetter, volumeSetter,
            value -> String.format("%.0f%%", value * 100)
        );
        widgets.add(volumeSlider);
        currentY += WIDGET_HEIGHT + 8 + WIDGET_SPACING;

        // Sound selection button with truncated text if needed
        String soundButtonText = width < 100 ? "Select" : "Select Sound";
        ModernButton soundButton = new ModernButton(
            x, currentY, width, WIDGET_HEIGHT,
            soundButtonText,
            () -> client.setScreen(new EnhancedSoundSelectionScreen(this, runeType, soundGetter, soundSetter)),
            ModernButton.ButtonStyle.SECONDARY
        );
        widgets.add(soundButton);
        currentY += WIDGET_HEIGHT + WIDGET_SPACING;

        // Test sound button with truncated text if needed
        String testButtonText = width < 100 ? "🎵 Test" : "🎵 Test Sound";
        ModernButton testButton = new ModernButton(
            x, currentY, width, WIDGET_HEIGHT,
            testButtonText,
            () -> testRuneSound(runeType, soundGetter.get(), volumeGetter.get()),
            ModernButton.ButtonStyle.WARNING
        );
        widgets.add(testButton);
    }

    /**
     * Creates compact individual audio controls for a specific rune type in a single row layout
     */
    private void initRuneAudioColumnScrollable(net.marko.runicmod.gui.modern.ScrollableContainer container,
                                             int x, int y, int width, String title,
                                             java.util.function.Supplier<Boolean> enableGetter,
                                             java.util.function.Consumer<Boolean> enableSetter,
                                             java.util.function.Supplier<Float> volumeGetter,
                                             java.util.function.Consumer<Float> volumeSetter,
                                             java.util.function.Supplier<String> soundGetter,
                                             java.util.function.Consumer<String> soundSetter,
                                             RuneType runeType) {

        // Create a compact layout for each rune type
        int compactHeight = 22; // Reduced height for compactness
        int compactSpacing = 4; // Reduced spacing
        int currentY = y;

        // Row 1: Enable toggle (full width)
        ModernToggle enableToggle = new ModernToggle(
            x, currentY, width, compactHeight,
            title,
            enableGetter, enableSetter
        );
        container.addChild(enableToggle);
        currentY += compactHeight + compactSpacing;

        // Row 2: Volume slider (65% width) + Test button (35% width)
        int sliderWidth = (int)(width * 0.63);
        int buttonWidth = width - sliderWidth - 4; // 4px gap

        ModernSlider volumeSlider = new ModernSlider(
            x, currentY, sliderWidth, compactHeight,
            "Vol", 0.0f, 1.0f, // Shortened label for compactness
            volumeGetter, volumeSetter,
            value -> String.format("%.0f%%", value * 100)
        );
        container.addChild(volumeSlider);

        // Test sound button (compact)
        ModernButton testButton = new ModernButton(
            x + sliderWidth + 4, currentY, buttonWidth, compactHeight,
            "Test",
            () -> testRuneSound(runeType, soundGetter.get(), volumeGetter.get()),
            ModernButton.ButtonStyle.WARNING
        );
        container.addChild(testButton);
        currentY += compactHeight + compactSpacing;

        // Row 3: Sound selection button (full width)
        ModernButton soundButton = new ModernButton(
            x, currentY, width, compactHeight,
            "Select Sound",
            () -> client.setScreen(new EnhancedSoundSelectionScreen(this, runeType, soundGetter, soundSetter)),
            ModernButton.ButtonStyle.SECONDARY
        );
        container.addChild(soundButton);
    }

    /**
     * Tests the sound for a specific rune type
     */
    private void testRuneSound(RuneType runeType, String soundId, float volume) {
        try {
            // Play the sound with master volume applied
            float masterVolume = config.getSoundVolume();
            float finalVolume = volume * masterVolume;

            // Use SoundManager to play the test sound
            net.marko.runicmod.sound.SoundManager.playTestSound(soundId, finalVolume);

            LOGGER.info("Testing {} sound: {} at volume {}", runeType.getDisplayName(), soundId, finalVolume);
        } catch (Exception e) {
            LOGGER.error("Error testing sound for {}", runeType.getDisplayName(), e);
        }
    }
    
    private void initPositionTab() {
        widgets.clear();

        int contentX = tabContainer.getContentX() + MARGIN;
        int contentY = tabContainer.getContentY() + MARGIN;
        int contentWidth = tabContainer.getContentWidth() - MARGIN * 2;
        int currentY = contentY;

        // Two-column layout for main controls with better spacing
        int leftColumnWidth = (contentWidth - COLUMN_SPACING) / 2;
        int rightColumnWidth = leftColumnWidth;
        int rightColumnX = contentX + leftColumnWidth + COLUMN_SPACING;

        // Left Column - Position Adjustment Button
        ModernButton positionButton = new ModernButton(
            contentX, currentY, leftColumnWidth, WIDGET_HEIGHT,
            "📍 Adjust Position",
            () -> client.setScreen(new TextPositionScreen(this)),
            ModernButton.ButtonStyle.PRIMARY
        );
        widgets.add(positionButton);

        // Right Column - Reset Position Button
        ModernButton resetButton = new ModernButton(
            rightColumnX, currentY, rightColumnWidth, WIDGET_HEIGHT,
            "🔄 Reset Position",
            () -> {
                config.setTitleX(-1);
                config.setTitleY(20);
                config.setTitleScale(0.8f);
            }, ModernButton.ButtonStyle.WARNING
        );
        widgets.add(resetButton);
        currentY += WIDGET_HEIGHT + SECTION_SPACING;

        // Quick Position Presets - Six columns with proper spacing for 16:9 interface
        int buttonWidth = (contentWidth - COLUMN_SPACING * 5) / 6;

        // Use full descriptive labels with advanced text fitting system
        String[] buttonTexts = new String[]{"Top Left", "Top Center", "Top Right", "Center Left", "Center", "Bottom"};

        ModernButton topLeftButton = new ModernButton(
            contentX, currentY, buttonWidth, WIDGET_HEIGHT,
            buttonTexts[0],
            () -> {
                config.setTitleX(20);
                config.setTitleY(20);
            }, ModernButton.ButtonStyle.SECONDARY
        );
        widgets.add(topLeftButton);

        ModernButton topCenterButton = new ModernButton(
            contentX + buttonWidth + COLUMN_SPACING, currentY, buttonWidth, WIDGET_HEIGHT,
            buttonTexts[1],
            () -> {
                config.setTitleX(-1);
                config.setTitleY(20);
            }, ModernButton.ButtonStyle.SECONDARY
        );
        widgets.add(topCenterButton);

        ModernButton topRightButton = new ModernButton(
            contentX + (buttonWidth + COLUMN_SPACING) * 2, currentY, buttonWidth, WIDGET_HEIGHT,
            buttonTexts[2],
            () -> {
                config.setTitleX(width - 200);
                config.setTitleY(20);
            }, ModernButton.ButtonStyle.SECONDARY
        );
        widgets.add(topRightButton);

        ModernButton centerLeftButton = new ModernButton(
            contentX + (buttonWidth + COLUMN_SPACING) * 3, currentY, buttonWidth, WIDGET_HEIGHT,
            buttonTexts[3],
            () -> {
                config.setTitleX(20);
                config.setTitleY(height / 2);
            }, ModernButton.ButtonStyle.SECONDARY
        );
        widgets.add(centerLeftButton);

        ModernButton centerButton = new ModernButton(
            contentX + (buttonWidth + COLUMN_SPACING) * 4, currentY, buttonWidth, WIDGET_HEIGHT,
            buttonTexts[4],
            () -> {
                config.setTitleX(-1);
                config.setTitleY(height / 2);
            }, ModernButton.ButtonStyle.SECONDARY
        );
        widgets.add(centerButton);

        ModernButton bottomButton = new ModernButton(
            contentX + (buttonWidth + COLUMN_SPACING) * 5, currentY, buttonWidth, WIDGET_HEIGHT,
            buttonTexts[5],
            () -> {
                config.setTitleX(-1);
                config.setTitleY(height - 100);
            }, ModernButton.ButtonStyle.SECONDARY
        );
        widgets.add(bottomButton);
    }
    
    private void initGeneralTab() {
        widgets.clear();

        int contentX = tabContainer.getContentX() + MARGIN;
        int contentY = tabContainer.getContentY() + MARGIN;
        int contentWidth = tabContainer.getContentWidth() - MARGIN * 2;
        int contentHeight = tabContainer.getContentHeight() - MARGIN * 2;

        // Create scrollable container for general content
        net.marko.runicmod.gui.modern.ScrollableContainer scrollContainer =
            new net.marko.runicmod.gui.modern.ScrollableContainer(contentX, contentY, contentWidth, contentHeight);

        int currentY = contentY;

        // Two-column layout with compact spacing for wider interface
        int leftColumnWidth = (contentWidth - COLUMN_SPACING) / 2;
        int rightColumnWidth = leftColumnWidth;
        int rightColumnX = contentX + leftColumnWidth + COLUMN_SPACING;

        // Left Column - Rune Type Toggles with compact spacing
        ModernToggle runicToggle = new ModernToggle(
            contentX, currentY, leftColumnWidth, WIDGET_HEIGHT,
            "🔴 Rune Obstruction Detection",
            config::isEnableRunicObstruction, config::setEnableRunicObstruction
        );
        scrollContainer.addChild(runicToggle);

        // Right Column - Clear Button (moved up to replace detection range)
        ModernButton clearButton = new ModernButton(
            rightColumnX, currentY, rightColumnWidth, WIDGET_HEIGHT,
            "🗑️ Clear All Effects",
            CustomTextRenderer::clearAllEffects, ModernButton.ButtonStyle.ERROR
        );
        scrollContainer.addChild(clearButton);
        currentY += WIDGET_HEIGHT + WIDGET_SPACING;

        // Left Column - Sky Stepper Toggle
        ModernToggle stepperToggle = new ModernToggle(
            contentX, currentY, leftColumnWidth, WIDGET_HEIGHT,
            "🔵 Sky Stepper Detection",
            config::isEnableSkyStepperDetection, config::setEnableSkyStepperDetection
        );
        scrollContainer.addChild(stepperToggle);

        // Right Column - Reset Button (moved up)
        ModernButton resetButton = new ModernButton(
            rightColumnX, currentY, rightColumnWidth, WIDGET_HEIGHT,
            "🔄 Reset All Settings",
            this::resetAllSettings, ModernButton.ButtonStyle.WARNING
        );
        scrollContainer.addChild(resetButton);
        currentY += WIDGET_HEIGHT + WIDGET_SPACING;

        // Left Column - Dasher Toggle (final row)
        ModernToggle dasherToggle = new ModernToggle(
            contentX, currentY, leftColumnWidth, WIDGET_HEIGHT,
            "🟡 Dasher Detection",
            config::isEnableDasherDetection, config::setEnableDasherDetection
        );
        scrollContainer.addChild(dasherToggle);

        // Store the scrollable container
        generalScrollContainer = scrollContainer;
    }
    
    private void resetAllSettings() {
        config.setShowTitle(true);
        config.setTitleScale(0.8f);
        config.setTitleX(-1);
        config.setTitleY(20);
        config.setPlaySounds(true);
        config.setSoundVolume(1.0f);
        config.setMaxDistance(25);
        config.setEnableRunicObstruction(true);
        config.setEnableSkyStepperDetection(true);
        config.setEnableDasherDetection(true);
        config.saveConfig();

        // Refresh current tab
        switch (tabContainer.getSelectedTab()) {
            case 0: initDisplayTab(); break;
            case 1: initAudioTab(); break;
            case 2: initPositionTab(); break;
            case 3: initGeneralTab(); break;
        }
    }
    
    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // Handle scrollable container clicks first
        String currentTab = tabContainer != null ? tabContainer.getCurrentTab() : "";
        if ("Audio".equals(currentTab) && audioScrollContainer != null) {
            if (audioScrollContainer.mouseClicked(mouseX, mouseY, button)) {
                return true;
            }
        } else if ("General".equals(currentTab) && generalScrollContainer != null) {
            if (generalScrollContainer.mouseClicked(mouseX, mouseY, button)) {
                return true;
            }
        }

        // Handle tab container clicks
        if (tabContainer.mouseClicked(mouseX, mouseY, button)) {
            return true;
        }

        // Handle widget clicks
        for (ModernWidget widget : widgets) {
            if (widget.mouseClicked(mouseX, mouseY, button)) {
                return true;
            }
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    @Override
    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        // Handle scrollable container releases
        String currentTab = tabContainer != null ? tabContainer.getCurrentTab() : "";
        if ("Audio".equals(currentTab) && audioScrollContainer != null) {
            audioScrollContainer.mouseReleased(mouseX, mouseY, button);
        } else if ("General".equals(currentTab) && generalScrollContainer != null) {
            generalScrollContainer.mouseReleased(mouseX, mouseY, button);
        }

        // Handle widget releases
        for (ModernWidget widget : widgets) {
            if (widget.mouseReleased(mouseX, mouseY, button)) {
                return true;
            }
        }

        return super.mouseReleased(mouseX, mouseY, button);
    }

    @Override
    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        // Handle scrollable container dragging
        String currentTab = tabContainer != null ? tabContainer.getCurrentTab() : "";
        if ("Audio".equals(currentTab) && audioScrollContainer != null) {
            if (audioScrollContainer.mouseDragged(mouseX, mouseY, button, deltaX, deltaY)) {
                return true;
            }
        } else if ("General".equals(currentTab) && generalScrollContainer != null) {
            if (generalScrollContainer.mouseDragged(mouseX, mouseY, button, deltaX, deltaY)) {
                return true;
            }
        }

        // Handle widget drags
        for (ModernWidget widget : widgets) {
            if (widget.mouseDragged(mouseX, mouseY, button, deltaX, deltaY)) {
                return true;
            }
        }

        return super.mouseDragged(mouseX, mouseY, button, deltaX, deltaY);
    }
    
    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double horizontalAmount, double verticalAmount) {
        // Handle scrollable container scrolling
        String currentTab = tabContainer != null ? tabContainer.getCurrentTab() : "";
        if ("Audio".equals(currentTab) && audioScrollContainer != null) {
            if (audioScrollContainer.mouseScrolled(mouseX, mouseY, horizontalAmount, verticalAmount)) {
                return true;
            }
        } else if ("General".equals(currentTab) && generalScrollContainer != null) {
            if (generalScrollContainer.mouseScrolled(mouseX, mouseY, horizontalAmount, verticalAmount)) {
                return true;
            }
        }

        return super.mouseScrolled(mouseX, mouseY, horizontalAmount, verticalAmount);
    }

    @Override
    public void renderBackground(DrawContext context, int mouseX, int mouseY, float delta) {
        // Override to prevent default background blur - render nothing
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Calculate centered position for responsive GUI
        int guiX = (width - guiWidth) / 2;
        int guiY = (height - guiHeight) / 2;

        // Draw modern GUI background with depth
        // Subtle shadow for elevation
        context.fill(guiX + 2, guiY + 2, guiX + guiWidth + 2, guiY + guiHeight + 2, ModernWidget.SHADOW_COLOR);

        // Main background with modern color
        context.fill(guiX, guiY, guiX + guiWidth, guiY + guiHeight, ModernWidget.BACKGROUND_COLOR);

        // Modern border with accent
        context.drawBorder(guiX, guiY, guiWidth, guiHeight, ModernWidget.BORDER_COLOR);

        // Render tab container
        if (tabContainer != null) {
            tabContainer.render(context, mouseX, mouseY, delta);
        }

        // Render modern widgets
        for (ModernWidget widget : widgets) {
            widget.render(context, mouseX, mouseY, delta);
        }

        // Render scrollable containers based on current tab
        String currentTab = tabContainer != null ? tabContainer.getCurrentTab() : "";
        if ("Audio".equals(currentTab) && audioScrollContainer != null) {
            audioScrollContainer.renderWidget(context, mouseX, mouseY, delta);
        } else if ("General".equals(currentTab) && generalScrollContainer != null) {
            generalScrollContainer.renderWidget(context, mouseX, mouseY, delta);
        }

        // Render buttons manually to avoid super.render() which might cause blur
        for (var child : children()) {
            if (child instanceof ModernButton modernButton) {
                modernButton.render(context, mouseX, mouseY, delta);
            }
        }

        // Draw modern title with enhanced typography
        var textRenderer = MinecraftClient.getInstance().textRenderer;
        String titleText = "Rune Utils";
        int titleX = guiX + (guiWidth - textRenderer.getWidth(titleText)) / 2;
        int titleY = guiY + 10;

        // Enhanced title shadow for depth
        context.drawText(textRenderer, titleText, titleX + 1, titleY + 1, ModernWidget.SHADOW_STRONG, false);
        // Main title text with modern color
        context.drawText(textRenderer, titleText, titleX, titleY, ModernWidget.TEXT_COLOR, false);

        // Draw version info with modern styling
        String versionText = "by Cerv";
        int versionX = guiX + guiWidth - textRenderer.getWidth(versionText) - MARGIN;
        int versionY = guiY + guiHeight - 18;
        context.drawText(textRenderer, versionText, versionX, versionY, ModernWidget.TEXT_MUTED, false);

        // Add subtle accent line under title
        int lineY = titleY + textRenderer.fontHeight + 4;
        int lineWidth = textRenderer.getWidth(titleText);
        context.fill(titleX, lineY, titleX + lineWidth, lineY + 1, ModernWidget.ACCENT_COLOR);
    }
    
    @Override
    public void close() {
        client.setScreen(parent);
    }

    @Override
    public boolean shouldPause() {
        return false; // Don't pause the game and don't blur the background
    }


}
