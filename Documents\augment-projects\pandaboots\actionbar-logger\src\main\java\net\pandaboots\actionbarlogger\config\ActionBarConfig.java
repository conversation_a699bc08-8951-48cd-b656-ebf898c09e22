package net.pandaboots.actionbarlogger.config;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import net.fabricmc.loader.api.FabricLoader;
import net.pandaboots.actionbarlogger.ActionBarLogger;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

public class ActionBarConfig {
    private static ActionBarConfig instance;
    private static final Gson GSON = new GsonBuilder().setPrettyPrinting().create();
    private static final String CONFIG_FILE_NAME = "actionbar-logger.json";

    // Configuration fields
    private boolean enabled = true;
    private String logFilePath = "config/actionbar-logger/actionbar.log";
    private boolean includeTimestamp = true;
    private boolean createDirectories = true;
    private int maxLogFileSize = 10; // MB
    private boolean rotateLogFiles = true;

    // Status display settings
    private boolean showStatusDisplay = true;
    private int statusX = -1; // -1 = center
    private int statusY = 50; // pixels from top
    private float statusScale = 1.0f;
    private boolean isDraggable = true;

    // Visual styling settings
    private int backgroundOpacity = 80; // 0-100%
    private int borderThickness = 2; // 0-5 pixels
    private int cornerRadius = 6; // 0-15 pixels
    private int shadowIntensity = 50; // 0-100%

    // Color settings (hex strings)
    private String lazyColor = "#4A90E2";
    private String aggressiveColor = "#E74C3C";
    private String playfulColor = "#2ECC71";
    private String textColor = "#FFFFFF";
    private String backgroundColor = "#000000";
    private String borderColor = "#404040";

    // Display options
    private boolean showTimer = true;
    private boolean showEffectDescriptions = true;
    private boolean showProgressBar = true;
    private boolean showMoodIcons = true;

    // Anchor point (0-8: TL, TC, TR, CL, C, CR, BL, BC, BR)
    private int anchorPoint = 1; // Top-center by default

    private ActionBarConfig() {
        // Private constructor for singleton
    }

    public static ActionBarConfig getInstance() {
        if (instance == null) {
            instance = loadConfig();
        }
        return instance;
    }

    private static ActionBarConfig loadConfig() {
        Path configDir = FabricLoader.getInstance().getConfigDir();
        Path configFile = configDir.resolve(CONFIG_FILE_NAME);

        if (Files.exists(configFile)) {
            try {
                String json = Files.readString(configFile);
                ActionBarConfig config = GSON.fromJson(json, ActionBarConfig.class);
                ActionBarLogger.LOGGER.info("Loaded configuration from {}", configFile);
                return config;
            } catch (IOException e) {
                ActionBarLogger.LOGGER.error("Failed to load config file, using defaults", e);
            }
        }

        // Create default config
        ActionBarConfig defaultConfig = new ActionBarConfig();
        defaultConfig.saveConfig();
        return defaultConfig;
    }

    public void saveConfig() {
        Path configDir = FabricLoader.getInstance().getConfigDir();
        Path configFile = configDir.resolve(CONFIG_FILE_NAME);

        try {
            Files.createDirectories(configDir);
            String json = GSON.toJson(this);
            Files.writeString(configFile, json);
            ActionBarLogger.LOGGER.debug("Saved configuration to {}", configFile);
        } catch (IOException e) {
            ActionBarLogger.LOGGER.error("Failed to save config file", e);
        }
    }

    // Getters and setters
    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        saveConfig();
    }

    public String getLogFilePath() {
        return logFilePath;
    }

    public void setLogFilePath(String logFilePath) {
        this.logFilePath = logFilePath;
        saveConfig();
    }

    public boolean isIncludeTimestamp() {
        return includeTimestamp;
    }

    public void setIncludeTimestamp(boolean includeTimestamp) {
        this.includeTimestamp = includeTimestamp;
        saveConfig();
    }

    public boolean isCreateDirectories() {
        return createDirectories;
    }

    public void setCreateDirectories(boolean createDirectories) {
        this.createDirectories = createDirectories;
        saveConfig();
    }

    public int getMaxLogFileSize() {
        return maxLogFileSize;
    }

    public void setMaxLogFileSize(int maxLogFileSize) {
        this.maxLogFileSize = maxLogFileSize;
        saveConfig();
    }

    public boolean isRotateLogFiles() {
        return rotateLogFiles;
    }

    public void setRotateLogFiles(boolean rotateLogFiles) {
        this.rotateLogFiles = rotateLogFiles;
        saveConfig();
    }

    // Status display getters and setters
    public boolean isShowStatusDisplay() {
        return showStatusDisplay;
    }

    public void setShowStatusDisplay(boolean showStatusDisplay) {
        this.showStatusDisplay = showStatusDisplay;
        saveConfig();
    }

    public int getStatusX() {
        return statusX;
    }

    public void setStatusX(int statusX) {
        this.statusX = statusX;
        saveConfig();
    }

    public int getStatusY() {
        return statusY;
    }

    public void setStatusY(int statusY) {
        this.statusY = statusY;
        saveConfig();
    }

    public float getStatusScale() {
        return statusScale;
    }

    public void setStatusScale(float statusScale) {
        this.statusScale = statusScale;
        saveConfig();
    }

    public boolean isDraggable() {
        return isDraggable;
    }

    public void setDraggable(boolean draggable) {
        isDraggable = draggable;
        saveConfig();
    }

    // Visual styling getters and setters
    public int getBackgroundOpacity() {
        return backgroundOpacity;
    }

    public void setBackgroundOpacity(int backgroundOpacity) {
        this.backgroundOpacity = Math.max(0, Math.min(100, backgroundOpacity));
        saveConfig();
    }

    public int getBorderThickness() {
        return borderThickness;
    }

    public void setBorderThickness(int borderThickness) {
        this.borderThickness = Math.max(0, Math.min(5, borderThickness));
        saveConfig();
    }

    public int getCornerRadius() {
        return cornerRadius;
    }

    public void setCornerRadius(int cornerRadius) {
        this.cornerRadius = Math.max(0, Math.min(15, cornerRadius));
        saveConfig();
    }

    public int getShadowIntensity() {
        return shadowIntensity;
    }

    public void setShadowIntensity(int shadowIntensity) {
        this.shadowIntensity = Math.max(0, Math.min(100, shadowIntensity));
        saveConfig();
    }

    // Color getters and setters
    public String getLazyColor() {
        return lazyColor;
    }

    public void setLazyColor(String lazyColor) {
        this.lazyColor = lazyColor;
        saveConfig();
    }

    public String getAggressiveColor() {
        return aggressiveColor;
    }

    public void setAggressiveColor(String aggressiveColor) {
        this.aggressiveColor = aggressiveColor;
        saveConfig();
    }

    public String getPlayfulColor() {
        return playfulColor;
    }

    public void setPlayfulColor(String playfulColor) {
        this.playfulColor = playfulColor;
        saveConfig();
    }

    public String getTextColor() {
        return textColor;
    }

    public void setTextColor(String textColor) {
        this.textColor = textColor;
        saveConfig();
    }

    public String getBackgroundColor() {
        return backgroundColor;
    }

    public void setBackgroundColor(String backgroundColor) {
        this.backgroundColor = backgroundColor;
        saveConfig();
    }

    public String getBorderColor() {
        return borderColor;
    }

    public void setBorderColor(String borderColor) {
        this.borderColor = borderColor;
        saveConfig();
    }

    // Display options getters and setters
    public boolean isShowTimer() {
        return showTimer;
    }

    public void setShowTimer(boolean showTimer) {
        this.showTimer = showTimer;
        saveConfig();
    }

    public boolean isShowEffectDescriptions() {
        return showEffectDescriptions;
    }

    public void setShowEffectDescriptions(boolean showEffectDescriptions) {
        this.showEffectDescriptions = showEffectDescriptions;
        saveConfig();
    }

    public boolean isShowProgressBar() {
        return showProgressBar;
    }

    public void setShowProgressBar(boolean showProgressBar) {
        this.showProgressBar = showProgressBar;
        saveConfig();
    }

    public boolean isShowMoodIcons() {
        return showMoodIcons;
    }

    public void setShowMoodIcons(boolean showMoodIcons) {
        this.showMoodIcons = showMoodIcons;
        saveConfig();
    }

    public int getAnchorPoint() {
        return anchorPoint;
    }

    public void setAnchorPoint(int anchorPoint) {
        this.anchorPoint = Math.max(0, Math.min(8, anchorPoint));
        saveConfig();
    }

    // Helper methods for color conversion
    public int getLazyColorInt() {
        return parseHexColor(lazyColor, 0x4A90E2);
    }

    public int getAggressiveColorInt() {
        return parseHexColor(aggressiveColor, 0xE74C3C);
    }

    public int getPlayfulColorInt() {
        return parseHexColor(playfulColor, 0x2ECC71);
    }

    public int getTextColorInt() {
        return parseHexColor(textColor, 0xFFFFFF);
    }

    public int getBackgroundColorInt() {
        return parseHexColor(backgroundColor, 0x000000);
    }

    public int getBorderColorInt() {
        return parseHexColor(borderColor, 0x404040);
    }

    private int parseHexColor(String hex, int defaultColor) {
        try {
            if (hex.startsWith("#")) {
                hex = hex.substring(1);
            }
            return (int) Long.parseLong(hex, 16);
        } catch (NumberFormatException e) {
            return defaultColor;
        }
    }
}
