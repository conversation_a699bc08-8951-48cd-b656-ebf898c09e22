package net.pandaboots.pandabootsstatus.gui;

import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.text.Text;
import net.pandaboots.pandabootsstatus.config.ActionBarConfig;
import net.pandaboots.pandabootsstatus.gui.modern.ModernButton;
import net.pandaboots.pandabootsstatus.gui.modern.ModernToggle;
import net.pandaboots.pandabootsstatus.gui.modern.ModernWidget;
import net.pandaboots.pandabootsstatus.gui.modern.ModernTextInput;
import net.pandaboots.pandabootsstatus.gui.modern.EmojiPreview;
import net.pandaboots.pandabootsstatus.render.SafeEmojiRenderer;

import java.util.ArrayList;
import java.util.List;

/**
 * Dedicated settings screen for custom emoji configuration
 */
public class CustomEmojiSettingsScreen extends Screen {
    private final Screen parent;
    private final ActionBarConfig config;
    private final List<ModernWidget> widgets = new ArrayList<>();
    
    // UI Constants
    private static final int WIDGET_HEIGHT = 30;
    private static final int WIDGET_SPACING = 8;
    private static final int SECTION_SPACING = 16;
    private static final int PADDING = 20;
    
    public CustomEmojiSettingsScreen(Screen parent) {
        super(Text.literal("Custom Emoji Configuration"));
        this.parent = parent;
        this.config = ActionBarConfig.getInstance();
    }
    
    @Override
    protected void init() {
        super.init();
        widgets.clear();
        
        int contentWidth = Math.min(700, this.width - PADDING * 2);
        int contentX = (this.width - contentWidth) / 2;
        int currentY = 60;
        
        // Master toggle for custom emojis
        ModernToggle customEmojiToggle = new ModernToggle(
            contentX, currentY, contentWidth, WIDGET_HEIGHT,
            "🎨 Enable Custom Emojis",
            config::isUseCustomEmojis, 
            enabled -> {
                config.setUseCustomEmojis(enabled);
                SafeEmojiRenderer.recheckRenderers(); // Refresh emoji system
            }
        );
        widgets.add(customEmojiToggle);
        currentY += WIDGET_HEIGHT + SECTION_SPACING;
        
        // Instructions text
        currentY += 10; // Extra space for instructions
        
        // Custom emoji configuration controls
        currentY = addEmojiControl(contentX, currentY, contentWidth, 
            "🐼 Panda Emoji (Title)", "panda",
            "https://example.com/panda.png",
            config::getCustomPandaEmojiUrl, 
            config::setCustomPandaEmojiUrl);
        
        currentY = addEmojiControl(contentX, currentY, contentWidth,
            "😡 Aggressive Emoji", "aggressive", 
            "https://example.com/angry.png",
            config::getCustomAggressiveEmojiUrl,
            config::setCustomAggressiveEmojiUrl);
        
        currentY = addEmojiControl(contentX, currentY, contentWidth,
            "😄 Playful Emoji", "playful",
            "https://example.com/happy.png", 
            config::getCustomPlayfulEmojiUrl,
            config::setCustomPlayfulEmojiUrl);
        
        currentY = addEmojiControl(contentX, currentY, contentWidth,
            "😴 Lazy Emoji", "lazy",
            "https://example.com/sleepy.png",
            config::getCustomLazyEmojiUrl,
            config::setCustomLazyEmojiUrl);
        
        currentY += SECTION_SPACING;
        
        // Back button
        ModernButton backButton = new ModernButton(
            contentX, currentY, 100, WIDGET_HEIGHT,
            "Back", this::close, ModernButton.ButtonStyle.SECONDARY
        );
        widgets.add(backButton);
        
        // Reset button
        ModernButton resetButton = new ModernButton(
            contentX + 110, currentY, 120, WIDGET_HEIGHT,
            "Reset All", this::resetAllEmojis, ModernButton.ButtonStyle.DANGER
        );
        widgets.add(resetButton);
    }
    
    private int addEmojiControl(int x, int y, int width, String label, String emojiName, 
                               String placeholder, java.util.function.Supplier<String> getter, 
                               java.util.function.Consumer<String> setter) {
        
        // Create preview widget (left side)
        EmojiPreview preview = new EmojiPreview(x, y, emojiName, "");
        widgets.add(preview);
        
        // Create text input (right side)
        int inputX = x + 90; // Space for preview
        int inputWidth = width - 90;
        
        ModernTextInput textInput = new ModernTextInput(
            inputX, y, inputWidth, 45, // Taller for validation message
            label, placeholder, getter, setter,
            url -> {
                // Refresh emoji system when URL changes
                SafeEmojiRenderer.recheckRenderers();
                preview.refresh();
            }
        );
        widgets.add(textInput);
        
        return y + 60; // Height + spacing
    }
    
    private void resetAllEmojis() {
        config.setCustomPandaEmojiUrl("");
        config.setCustomAggressiveEmojiUrl("");
        config.setCustomPlayfulEmojiUrl("");
        config.setCustomLazyEmojiUrl("");
        config.setUseCustomEmojis(false);
        SafeEmojiRenderer.recheckRenderers();
        
        // Rebuild the screen to reflect changes
        this.init();
    }
    
    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Draw modern background
        context.fill(0, 0, this.width, this.height, ModernWidget.BACKGROUND_COLOR);
        
        // Draw title
        context.drawCenteredTextWithShadow(this.textRenderer, this.title, this.width / 2, 20, ModernWidget.TEXT_COLOR);
        
        // Draw instructions
        int contentWidth = Math.min(700, this.width - PADDING * 2);
        int contentX = (this.width - contentWidth) / 2;
        
        String instructions = "Configure custom emoji images using URLs or file paths. Images will be automatically resized to 8x8 pixels.";
        context.drawText(this.textRenderer, instructions, contentX, 85, ModernWidget.TEXT_COLOR, false);
        
        String instructions2 = "Leave fields empty to use default emojis. Changes are saved automatically.";
        context.drawText(this.textRenderer, instructions2, contentX, 97, ModernWidget.TEXT_COLOR, false);
        
        // Render modern widgets
        for (ModernWidget widget : widgets) {
            widget.render(context, mouseX, mouseY, delta);
        }
    }
    
    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // Handle modern widget clicks
        for (ModernWidget widget : widgets) {
            if (widget.mouseClicked(mouseX, mouseY, button)) {
                return true;
            }
        }
        return super.mouseClicked(mouseX, mouseY, button);
    }
    
    @Override
    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        // Handle modern widget dragging
        for (ModernWidget widget : widgets) {
            if (widget.mouseDragged(mouseX, mouseY, button, deltaX, deltaY)) {
                return true;
            }
        }
        return super.mouseDragged(mouseX, mouseY, button, deltaX, deltaY);
    }
    
    @Override
    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        // Handle modern widget releases
        for (ModernWidget widget : widgets) {
            if (widget.mouseReleased(mouseX, mouseY, button)) {
                return true;
            }
        }
        return super.mouseReleased(mouseX, mouseY, button);
    }
    
    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        // Handle text input key presses
        for (ModernWidget widget : widgets) {
            if (widget instanceof ModernTextInput) {
                ModernTextInput textInput = (ModernTextInput) widget;
                if (textInput.isTextFieldFocused() && textInput.keyPressed(keyCode, scanCode, modifiers)) {
                    return true;
                }
            }
        }
        return super.keyPressed(keyCode, scanCode, modifiers);
    }
    
    @Override
    public boolean charTyped(char chr, int modifiers) {
        // Handle text input character typing
        for (ModernWidget widget : widgets) {
            if (widget instanceof ModernTextInput) {
                ModernTextInput textInput = (ModernTextInput) widget;
                if (textInput.isTextFieldFocused() && textInput.charTyped(chr, modifiers)) {
                    return true;
                }
            }
        }
        return super.charTyped(chr, modifiers);
    }
    
    @Override
    public void tick() {
        super.tick();
        // Tick text input widgets
        for (ModernWidget widget : widgets) {
            if (widget instanceof ModernTextInput) {
                ((ModernTextInput) widget).tick();
            }
        }
    }
    
    @Override
    public void close() {
        // Return to parent screen
        if (this.client != null) {
            this.client.setScreen(parent);
        }
    }
}
