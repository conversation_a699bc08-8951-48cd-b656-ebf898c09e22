package net.marko.runicmod.sound;

import net.fabricmc.loader.api.FabricLoader;
import net.marko.runicmod.RunicMod;
import net.marko.runicmod.config.RunicModConfig;
import net.marko.runicmod.rune.RuneType;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.sound.PositionedSoundInstance;
import net.minecraft.client.sound.SoundInstance;
import net.minecraft.sound.SoundEvent;
import net.minecraft.util.Identifier;
import org.slf4j.Logger;

import javax.sound.sampled.*;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.List;

/**
 * Manages sound playback for the Runic Mod
 */
public class SoundManager {
    private static final Logger LOGGER = RunicMod.LOGGER;

    // Directory for custom sounds
    private static final Path SOUNDS_DIR = FabricLoader.getInstance().getConfigDir().resolve("runic-mod").resolve("sounds");

    // Initialize the sounds directory
    static {
        try {
            if (!Files.exists(SOUNDS_DIR)) {
                Files.createDirectories(SOUNDS_DIR);
                LOGGER.info("Created sounds directory at {}", SOUNDS_DIR);
            }
        } catch (IOException e) {
            LOGGER.error("Failed to create sounds directory", e);
        }
    }

    /**
     * Plays the configured sound for a specific rune type
     * @param runeType The type of rune effect
     * @param volume The volume to play at (0.0 to 1.0)
     */
    public static void playSound(RuneType runeType, float volume) {
        RunicModConfig config = RunicModConfig.getInstance();

        if (!config.isPlaySounds()) {
            return;
        }

        // Get the sound for this specific rune type
        String soundId = getSoundForRuneType(runeType, config);

        if (config.isUseCustomSound() && !config.getCustomSoundPath().isEmpty()) {
            playCustomSound(config.getCustomSoundPath(), volume);
        } else {
            playMinecraftSound(soundId, volume);
        }
    }

    /**
     * Legacy method for compatibility
     * @param volume The volume to play at (0.0 to 1.0)
     */
    public static void playSound(float volume) {
        playSound(RuneType.RUNIC_OBSTRUCTION, volume);
    }

    /**
     * Gets the sound ID for a specific rune type
     * @param runeType The rune type
     * @param config The mod config
     * @return The sound ID to play
     */
    private static String getSoundForRuneType(RuneType runeType, RunicModConfig config) {
        switch (runeType) {
            case RUNIC_OBSTRUCTION:
                return config.getSelectedSound();
            case SKY_STEPPER:
                return config.getSkyStepperSound();
            case DASHER:
                return config.getDasherSound();
            default:
                return config.getSelectedSound();
        }
    }

    /**
     * Plays a Minecraft sound (public method for GUI)
     * @param soundId The sound identifier
     * @param volume The volume to play at (0.0 to 1.0)
     */
    public static void playMinecraftSound(String soundId, float volume) {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player == null) return;

        try {
            // Parse the sound identifier
            Identifier id;
            if (soundId.contains(":")) {
                id = Identifier.of(soundId);
            } else {
                id = Identifier.of("minecraft", soundId);
            }

            // Create a sound event
            SoundEvent soundEvent = SoundEvent.of(id);

            // Create a positioned sound instance
            SoundInstance soundInstance = PositionedSoundInstance.master(soundEvent, volume, 1.0f);

            // Play the sound
            client.getSoundManager().play(soundInstance);

            LOGGER.debug("Played Minecraft sound: {}", soundId);
        } catch (Exception e) {
            LOGGER.error("Failed to play Minecraft sound: {}", soundId, e);
        }
    }

    /**
     * Plays a custom sound file
     * @param soundPath The path to the sound file
     * @param volume The volume to play at (0.0 to 1.0)
     */
    private static void playCustomSound(String soundPath, float volume) {
        try {
            // Get the full path to the sound file
            Path fullPath = SOUNDS_DIR.resolve(soundPath);

            // Check if the file exists
            if (!Files.exists(fullPath)) {
                LOGGER.error("Custom sound file not found: {}", fullPath);
                return;
            }

            // Play the sound in a separate thread to avoid blocking the game
            new Thread(() -> {
                try {
                    // Get an audio input stream
                    AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(fullPath.toFile());

                    // Get a clip
                    Clip clip = AudioSystem.getClip();

                    // Open the audio input stream
                    clip.open(audioInputStream);

                    // Set the volume
                    if (clip.isControlSupported(FloatControl.Type.MASTER_GAIN)) {
                        FloatControl gainControl = (FloatControl) clip.getControl(FloatControl.Type.MASTER_GAIN);
                        float dB = (float) (Math.log(volume) / Math.log(10.0) * 20.0);
                        gainControl.setValue(dB);
                    }

                    // Play the clip
                    clip.start();

                    LOGGER.debug("Played custom sound: {}", soundPath);
                } catch (Exception e) {
                    LOGGER.error("Failed to play custom sound: {}", soundPath, e);
                }
            }).start();
        } catch (Exception e) {
            LOGGER.error("Failed to play custom sound: {}", soundPath, e);
        }
    }

    /**
     * Imports a sound file into the sounds directory
     * @param sourceFile The source file to import
     * @return The name of the imported file, or null if import failed
     */
    public static String importSoundFile(File sourceFile) {
        try {
            // Create the sounds directory if it doesn't exist
            if (!Files.exists(SOUNDS_DIR)) {
                Files.createDirectories(SOUNDS_DIR);
            }

            // Get the destination file
            String fileName = sourceFile.getName();
            Path destPath = SOUNDS_DIR.resolve(fileName);

            // Copy the file
            Files.copy(sourceFile.toPath(), destPath, StandardCopyOption.REPLACE_EXISTING);

            LOGGER.info("Imported sound file: {}", fileName);

            return fileName;
        } catch (IOException e) {
            LOGGER.error("Failed to import sound file", e);
            return null;
        }
    }

    /**
     * Gets the directory where custom sounds are stored
     * @return The sounds directory
     */
    public static Path getSoundsDirectory() {
        return SOUNDS_DIR;
    }

    /**
     * Gets a list of custom sound files
     * @return A list of custom sound files
     */
    public static List<File> getCustomSoundFiles() {
        List<File> files = new ArrayList<>();
        File[] soundFiles = SOUNDS_DIR.toFile().listFiles();

        if (soundFiles != null) {
            for (File file : soundFiles) {
                if (file.isFile() && (file.getName().endsWith(".wav") ||
                                     file.getName().endsWith(".ogg") ||
                                     file.getName().endsWith(".mp3"))) {
                    files.add(file);
                }
            }
        }

        return files;
    }

    /**
     * Plays a test sound for GUI testing purposes
     * @param soundId The sound identifier
     * @param volume The volume to play at (0.0 to 1.0)
     */
    public static void playTestSound(String soundId, float volume) {
        playMinecraftSound(soundId, volume);
    }
}
