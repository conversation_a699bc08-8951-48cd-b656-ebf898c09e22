package net.marko.runicmod.config;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import net.fabricmc.loader.api.FabricLoader;
import net.marko.runicmod.RunicMod;
import org.slf4j.Logger;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;

/**
 * Configuration class for Rune Utils
 */
public class RunicModConfig {
    private static final Logger LOGGER = RunicMod.LOGGER;
    private static final Gson GSON = new GsonBuilder().setPrettyPrinting().create();
    private static final File CONFIG_FILE = FabricLoader.getInstance().getConfigDir().resolve("rune-utils.json").toFile();

    private static RunicModConfig INSTANCE;

    // Display settings
    private int titleX = -1; // -1 = center
    private int titleY = 20; // pixels from top
    private float titleScale = 0.8f; // Reduced default size
    private int titleColor = 0xFF0000; // Red
    private String titleColorOption = "RED"; // Default color option
    private int customTitleColor = 0xFF0000; // Custom title color
    private boolean useCustomTitleColor = false; // Whether to use custom title color
    private boolean showTitle = true; // Whether to show the title
    private boolean isDraggable = true; // Whether the text can be dragged

    // Sound settings
    private boolean playSounds = true;
    private float soundVolume = 1.0f;
    private String selectedSound = "minecraft:entity.experience_orb.pickup"; // Default sound for runic obstruction
    private String skyStepperSound = "minecraft:entity.player.levelup"; // Default sound for sky stepper
    private String dasherSound = "minecraft:block.note_block.pling"; // Default sound for dasher
    private boolean useCustomSound = false;
    private String customSoundPath = ""; // Path to custom sound file

    // Distance settings
    private int maxDistance = 25; // blocks

    // Individual rune type toggles
    private boolean enableRunicObstruction = true;
    private boolean enableSkyStepperDetection = true;
    private boolean enableDasherDetection = true;

    // Individual audio settings for each rune type
    private boolean enableRunicObstructionSound = true;
    private boolean enableSkyStepperSound = true;
    private boolean enableDasherSound = true;

    private float runicObstructionVolume = 1.0f;
    private float skyStepperVolume = 1.0f;
    private float dasherVolume = 1.0f;

    private String runicObstructionSoundId = "minecraft:entity.experience_orb.pickup";
    private String skyStepperSoundId = "minecraft:entity.player.levelup";
    private String dasherSoundId = "minecraft:block.note_block.pling";

    /**
     * Gets the singleton instance of the config
     */
    public static RunicModConfig getInstance() {
        if (INSTANCE == null) {
            INSTANCE = loadConfig();
        }
        return INSTANCE;
    }

    /**
     * Loads the config from file
     */
    private static RunicModConfig loadConfig() {
        if (CONFIG_FILE.exists()) {
            try (FileReader reader = new FileReader(CONFIG_FILE)) {
                return GSON.fromJson(reader, RunicModConfig.class);
            } catch (IOException e) {
                LOGGER.error("Failed to load config", e);
            }
        }

        // If the file doesn't exist or there was an error, create a default config
        RunicModConfig config = new RunicModConfig();
        config.saveConfig();
        return config;
    }

    /**
     * Saves the config to file
     */
    public void saveConfig() {
        try {
            if (!CONFIG_FILE.exists()) {
                CONFIG_FILE.getParentFile().mkdirs();
                CONFIG_FILE.createNewFile();
            }

            try (FileWriter writer = new FileWriter(CONFIG_FILE)) {
                GSON.toJson(this, writer);
            }
        } catch (IOException e) {
            LOGGER.error("Failed to save config", e);
        }
    }

    // Getters and setters

    public int getTitleX() {
        return titleX;
    }

    public void setTitleX(int titleX) {
        this.titleX = titleX;
        saveConfig();
    }

    public int getTitleY() {
        return titleY;
    }

    public void setTitleY(int titleY) {
        this.titleY = titleY;
        saveConfig();
    }

    public float getTitleScale() {
        return titleScale;
    }

    public void setTitleScale(float titleScale) {
        this.titleScale = titleScale;
        saveConfig();
    }

    public int getTitleColor() {
        if (useCustomTitleColor) {
            return customTitleColor;
        } else if (ColorOption.fromName(titleColorOption) == ColorOption.CUSTOM) {
            return customTitleColor;
        } else {
            return ColorOption.fromName(titleColorOption).getColor();
        }
    }

    public void setTitleColor(int titleColor) {
        ColorOption option = ColorOption.fromColor(titleColor);
        if (option == ColorOption.CUSTOM) {
            this.customTitleColor = titleColor;
            this.titleColorOption = "CUSTOM";
        } else {
            this.titleColorOption = option.name();
        }
        this.titleColor = titleColor;
        saveConfig();
    }

    public String getTitleColorOption() {
        return titleColorOption;
    }

    public void setTitleColorOption(String titleColorOption) {
        this.titleColorOption = titleColorOption;
        if (ColorOption.fromName(titleColorOption) != ColorOption.CUSTOM) {
            this.titleColor = ColorOption.fromName(titleColorOption).getColor();
        }
        saveConfig();
    }

    public int getCustomTitleColor() {
        return customTitleColor;
    }

    public void setCustomTitleColor(int customTitleColor) {
        this.customTitleColor = customTitleColor;
        if (useCustomTitleColor || ColorOption.fromName(titleColorOption) == ColorOption.CUSTOM) {
            this.titleColor = customTitleColor;
        }
        saveConfig();
    }

    public boolean isUseCustomTitleColor() {
        return useCustomTitleColor;
    }

    public void setUseCustomTitleColor(boolean useCustomTitleColor) {
        this.useCustomTitleColor = useCustomTitleColor;
        if (useCustomTitleColor) {
            this.titleColor = customTitleColor;
        } else {
            if (ColorOption.fromName(titleColorOption) != ColorOption.CUSTOM) {
                this.titleColor = ColorOption.fromName(titleColorOption).getColor();
            }
        }
        saveConfig();
    }

    public boolean isDraggable() {
        return isDraggable;
    }

    public void setDraggable(boolean draggable) {
        this.isDraggable = draggable;
        saveConfig();
    }

    public boolean isPlaySounds() {
        return playSounds;
    }

    public void setPlaySounds(boolean playSounds) {
        this.playSounds = playSounds;
        saveConfig();
    }

    public float getSoundVolume() {
        return soundVolume;
    }

    public void setSoundVolume(float soundVolume) {
        this.soundVolume = soundVolume;
        saveConfig();
    }

    public String getSelectedSound() {
        return selectedSound;
    }

    public void setSelectedSound(String selectedSound) {
        this.selectedSound = selectedSound;
        saveConfig();
    }

    public String getSkyStepperSound() {
        return skyStepperSound;
    }

    public void setSkyStepperSound(String skyStepperSound) {
        this.skyStepperSound = skyStepperSound;
        saveConfig();
    }

    public String getDasherSound() {
        return dasherSound;
    }

    public void setDasherSound(String dasherSound) {
        this.dasherSound = dasherSound;
        saveConfig();
    }

    public boolean isUseCustomSound() {
        return useCustomSound;
    }

    public void setUseCustomSound(boolean useCustomSound) {
        this.useCustomSound = useCustomSound;
        saveConfig();
    }

    public String getCustomSoundPath() {
        return customSoundPath;
    }

    public void setCustomSoundPath(String customSoundPath) {
        this.customSoundPath = customSoundPath;
        saveConfig();
    }



    public int getMaxDistance() {
        return maxDistance;
    }

    public void setMaxDistance(int maxDistance) {
        this.maxDistance = maxDistance;
        saveConfig();
    }

    public boolean isShowTitle() {
        return showTitle;
    }

    public void setShowTitle(boolean showTitle) {
        this.showTitle = showTitle;
        saveConfig();
    }

    // Individual rune type toggle getters and setters

    public boolean isEnableRunicObstruction() {
        return enableRunicObstruction;
    }

    public void setEnableRunicObstruction(boolean enableRunicObstruction) {
        this.enableRunicObstruction = enableRunicObstruction;
        saveConfig();
    }

    public boolean isEnableSkyStepperDetection() {
        return enableSkyStepperDetection;
    }

    public void setEnableSkyStepperDetection(boolean enableSkyStepperDetection) {
        this.enableSkyStepperDetection = enableSkyStepperDetection;
        saveConfig();
    }

    public boolean isEnableDasherDetection() {
        return enableDasherDetection;
    }

    public void setEnableDasherDetection(boolean enableDasherDetection) {
        this.enableDasherDetection = enableDasherDetection;
        saveConfig();
    }

    // Individual audio settings getters and setters

    public boolean isEnableRunicObstructionSound() {
        return enableRunicObstructionSound;
    }

    public void setEnableRunicObstructionSound(boolean enableRunicObstructionSound) {
        this.enableRunicObstructionSound = enableRunicObstructionSound;
        saveConfig();
    }

    public boolean isEnableSkyStepperSound() {
        return enableSkyStepperSound;
    }

    public void setEnableSkyStepperSound(boolean enableSkyStepperSound) {
        this.enableSkyStepperSound = enableSkyStepperSound;
        saveConfig();
    }

    public boolean isEnableDasherSound() {
        return enableDasherSound;
    }

    public void setEnableDasherSound(boolean enableDasherSound) {
        this.enableDasherSound = enableDasherSound;
        saveConfig();
    }

    public float getRunicObstructionVolume() {
        return runicObstructionVolume;
    }

    public void setRunicObstructionVolume(float runicObstructionVolume) {
        this.runicObstructionVolume = runicObstructionVolume;
        saveConfig();
    }

    public float getSkyStepperVolume() {
        return skyStepperVolume;
    }

    public void setSkyStepperVolume(float skyStepperVolume) {
        this.skyStepperVolume = skyStepperVolume;
        saveConfig();
    }

    public float getDasherVolume() {
        return dasherVolume;
    }

    public void setDasherVolume(float dasherVolume) {
        this.dasherVolume = dasherVolume;
        saveConfig();
    }

    public String getRunicObstructionSoundId() {
        return runicObstructionSoundId;
    }

    public void setRunicObstructionSoundId(String runicObstructionSoundId) {
        this.runicObstructionSoundId = runicObstructionSoundId;
        saveConfig();
    }

    public String getSkyStepperSoundId() {
        return skyStepperSoundId;
    }

    public void setSkyStepperSoundId(String skyStepperSoundId) {
        this.skyStepperSoundId = skyStepperSoundId;
        saveConfig();
    }

    public String getDasherSoundId() {
        return dasherSoundId;
    }

    public void setDasherSoundId(String dasherSoundId) {
        this.dasherSoundId = dasherSoundId;
        saveConfig();
    }

}
