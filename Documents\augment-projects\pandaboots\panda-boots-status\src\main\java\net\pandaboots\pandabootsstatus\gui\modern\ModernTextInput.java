package net.pandaboots.pandabootsstatus.gui.modern;

import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.widget.TextFieldWidget;
import net.minecraft.text.Text;
import net.pandaboots.pandabootsstatus.render.CustomEmojiLoader;

import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * Modern text input widget for URL/file path input with validation
 */
public class ModernTextInput extends ModernWidget {
    private final TextFieldWidget textField;
    private final String label;
    private final String placeholder;
    private final Supplier<String> getter;
    private final Consumer<String> setter;
    private final Consumer<String> onChange;

    // Validation states
    private ValidationState validationState = ValidationState.EMPTY;
    private String validationMessage = "";
    private long lastValidationTime = 0;
    private static final long VALIDATION_DELAY = 500; // 500ms delay before validation

    public ModernTextInput(int x, int y, int width, int height, String label, String placeholder,
                          Supplier<String> getter, Consumer<String> setter, Consumer<String> onChange) {
        super(x, y, width, height, Text.literal(label));
        this.label = label;
        this.placeholder = placeholder;
        this.getter = getter;
        this.setter = setter;
        this.onChange = onChange;

        // Create text field widget
        this.textField = new TextFieldWidget(MinecraftClient.getInstance().textRenderer,
                                           x + 2, y + 20, width - 4, height - 22, Text.literal(label));
        this.textField.setPlaceholder(Text.literal(placeholder));
        this.textField.setText(getter.get());
        this.textField.setMaxLength(512);

        // Set change listener
        this.textField.setChangedListener(this::onTextChanged);
    }

    @Override
    protected void onPress() {
        // Focus the text field when clicked
        textField.setFocused(true);
    }
    
    private void onTextChanged(String text) {
        // Update config immediately
        setter.accept(text);
        
        // Trigger validation after delay
        lastValidationTime = System.currentTimeMillis();
        
        // Call onChange callback
        if (onChange != null) {
            onChange.accept(text);
        }
        
        // Start validation
        validateInput(text);
    }
    
    private void validateInput(String text) {
        if (text == null || text.trim().isEmpty()) {
            validationState = ValidationState.EMPTY;
            validationMessage = "";
            return;
        }
        
        String trimmed = text.trim();
        
        // Basic validation
        if (CustomEmojiLoader.isValidSource(trimmed)) {
            validationState = ValidationState.LOADING;
            validationMessage = "Validating...";
            
            // Perform async validation (simplified for now)
            validateAsync(trimmed);
        } else {
            validationState = ValidationState.INVALID;
            validationMessage = "Invalid URL or file path";
        }
    }
    
    private void validateAsync(String source) {
        // Simple validation - in a real implementation, this would test the URL/file
        new Thread(() -> {
            try {
                Thread.sleep(200); // Simulate validation delay
                
                // Basic checks
                if (source.startsWith("http://") || source.startsWith("https://")) {
                    // URL validation
                    try {
                        java.net.URI.create(source);
                        validationState = ValidationState.VALID;
                        validationMessage = "Valid URL";
                    } catch (Exception e) {
                        validationState = ValidationState.INVALID;
                        validationMessage = "Invalid URL format";
                    }
                } else {
                    // File path validation
                    try {
                        java.nio.file.Path path = java.nio.file.Paths.get(source);
                        if (java.nio.file.Files.exists(path)) {
                            validationState = ValidationState.VALID;
                            validationMessage = "File found";
                        } else {
                            validationState = ValidationState.INVALID;
                            validationMessage = "File not found";
                        }
                    } catch (Exception e) {
                        validationState = ValidationState.INVALID;
                        validationMessage = "Invalid file path";
                    }
                }
            } catch (InterruptedException e) {
                validationState = ValidationState.INVALID;
                validationMessage = "Validation interrupted";
            }
        }).start();
    }
    
    @Override
    protected void renderContent(DrawContext context, int mouseX, int mouseY, float delta) {
        // Update text field position
        textField.setX(getX() + 2);
        textField.setY(getY() + 20);
        textField.setWidth(getWidth() - 4);

        // Draw label
        context.drawText(MinecraftClient.getInstance().textRenderer, label, getX(), getY() + 2, TEXT_COLOR, false);

        // Draw validation status
        if (validationState != ValidationState.EMPTY) {
            String statusText = validationState.message;
            int statusColor = validationState.color;
            int statusX = getX() + getWidth() - MinecraftClient.getInstance().textRenderer.getWidth(statusText) - 5;
            context.drawText(MinecraftClient.getInstance().textRenderer, statusText, statusX, getY() + 2, statusColor, false);
        }

        // Draw background for text field
        context.fill(getX(), getY() + 18, getX() + getWidth(), getY() + getHeight(), PRIMARY_COLOR);
        context.drawBorder(getX(), getY() + 18, getWidth(), getHeight() - 18, isHovered ? ACCENT_COLOR : BORDER_COLOR);

        // Render text field
        textField.render(context, mouseX, mouseY, delta);

        // Draw validation message if any
        if (!validationMessage.isEmpty() && validationState != ValidationState.EMPTY) {
            int messageY = getY() + getHeight() + 2;
            context.drawText(MinecraftClient.getInstance().textRenderer, validationMessage, getX(), messageY, validationState.color, false);
        }
    }
    
    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        boolean result = super.mouseClicked(mouseX, mouseY, button);
        if (isMouseOver(mouseX, mouseY)) {
            textField.mouseClicked(mouseX, mouseY, button);
            textField.setFocused(true);
            return true;
        }
        return result;
    }

    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        if (textField.isFocused()) {
            return textField.keyPressed(keyCode, scanCode, modifiers);
        }
        return super.keyPressed(keyCode, scanCode, modifiers);
    }

    @Override
    public boolean charTyped(char chr, int modifiers) {
        if (textField.isFocused()) {
            return textField.charTyped(chr, modifiers);
        }
        return super.charTyped(chr, modifiers);
    }

    public void setFocused(boolean focused) {
        textField.setFocused(focused);
    }

    public boolean isTextFieldFocused() {
        return textField.isFocused();
    }

    public void tick() {
        // Text field tick if needed
    }
    
    public ValidationState getValidationState() {
        return validationState;
    }
    
    public String getValidationMessage() {
        return validationMessage;
    }
    
    // Validation state enum with colors
    public static class ValidationState {
        public static final ValidationState EMPTY = new ValidationState(0x888888, "");
        public static final ValidationState VALID = new ValidationState(0x44FF44, "✓ Valid");
        public static final ValidationState INVALID = new ValidationState(0xFF4444, "✗ Invalid");
        public static final ValidationState LOADING = new ValidationState(0xFFAA00, "⟳ Loading...");
        
        public final int color;
        public final String message;
        
        private ValidationState(int color, String message) {
            this.color = color;
            this.message = message;
        }
    }
}
