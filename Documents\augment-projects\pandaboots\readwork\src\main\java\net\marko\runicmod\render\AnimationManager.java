package net.marko.runicmod.render;

import net.marko.runicmod.RunicMod;
import org.slf4j.Logger;

import java.util.HashMap;
import java.util.Map;

/**
 * Manages animations for the mod
 */
public class AnimationManager {
    private static final Logger LOGGER = RunicMod.LOGGER;
    
    private static final Map<String, Animation> animations = new HashMap<>();

    /**
     * Represents an animation with timing and easing
     */
    public static class Animation {
        private final long startTime;
        private final long duration;
        private final float startValue;
        private final float endValue;
        private final EasingType easing;
        private boolean completed = false;

        public Animation(float startValue, float endValue, long duration, EasingType easing) {
            this.startTime = System.currentTimeMillis();
            this.duration = duration;
            this.startValue = startValue;
            this.endValue = endValue;
            this.easing = easing;
        }

        public float getCurrentValue() {
            if (completed) return endValue;
            
            long elapsed = System.currentTimeMillis() - startTime;
            if (elapsed >= duration) {
                completed = true;
                return endValue;
            }
            
            float progress = (float) elapsed / duration;
            float easedProgress = easing.apply(progress);
            
            return startValue + (endValue - startValue) * easedProgress;
        }

        public boolean isCompleted() {
            return completed || (System.currentTimeMillis() - startTime) >= duration;
        }
    }

    /**
     * Easing functions for smooth animations
     */
    public enum EasingType {
        LINEAR(t -> t),
        EASE_IN(t -> t * t),
        EASE_OUT(t -> 1 - (1 - t) * (1 - t)),
        EASE_IN_OUT(t -> t < 0.5f ? 2 * t * t : 1 - (float) Math.pow(-2 * t + 2, 2) / 2),
        BOUNCE_OUT(t -> {
            float n1 = 7.5625f;
            float d1 = 2.75f;
            
            if (t < 1 / d1) {
                return n1 * t * t;
            } else if (t < 2 / d1) {
                return n1 * (t -= 1.5f / d1) * t + 0.75f;
            } else if (t < 2.5 / d1) {
                return n1 * (t -= 2.25f / d1) * t + 0.9375f;
            } else {
                return n1 * (t -= 2.625f / d1) * t + 0.984375f;
            }
        });

        private final EasingFunction function;

        EasingType(EasingFunction function) {
            this.function = function;
        }

        public float apply(float t) {
            return function.apply(Math.max(0, Math.min(1, t)));
        }
    }

    @FunctionalInterface
    private interface EasingFunction {
        float apply(float t);
    }

    /**
     * Starts a new animation
     * @param id Unique identifier for the animation
     * @param startValue Starting value
     * @param endValue Ending value
     * @param duration Duration in milliseconds
     * @param easing Easing type
     */
    public static void startAnimation(String id, float startValue, float endValue, long duration, EasingType easing) {
        animations.put(id, new Animation(startValue, endValue, duration, easing));
    }

    /**
     * Gets the current value of an animation
     * @param id Animation identifier
     * @param defaultValue Default value if animation doesn't exist
     * @return Current animation value
     */
    public static float getAnimationValue(String id, float defaultValue) {
        Animation animation = animations.get(id);
        if (animation == null) return defaultValue;
        
        float value = animation.getCurrentValue();
        if (animation.isCompleted()) {
            animations.remove(id);
        }
        
        return value;
    }

    /**
     * Checks if an animation is running
     * @param id Animation identifier
     * @return true if animation is active
     */
    public static boolean isAnimationRunning(String id) {
        Animation animation = animations.get(id);
        return animation != null && !animation.isCompleted();
    }

    /**
     * Stops an animation
     * @param id Animation identifier
     */
    public static void stopAnimation(String id) {
        animations.remove(id);
    }

    /**
     * Clears all animations
     */
    public static void clearAllAnimations() {
        animations.clear();
    }

    /**
     * Updates all animations (call this every frame)
     */
    public static void tick() {
        animations.entrySet().removeIf(entry -> entry.getValue().isCompleted());
    }

    /**
     * Creates a fade-in animation
     * @param id Animation identifier
     * @param duration Duration in milliseconds
     */
    public static void fadeIn(String id, long duration) {
        startAnimation(id, 0.0f, 1.0f, duration, EasingType.EASE_OUT);
    }

    /**
     * Creates a fade-out animation
     * @param id Animation identifier
     * @param duration Duration in milliseconds
     */
    public static void fadeOut(String id, long duration) {
        startAnimation(id, 1.0f, 0.0f, duration, EasingType.EASE_IN);
    }

    /**
     * Creates a slide-in animation from the left
     * @param id Animation identifier
     * @param startX Starting X position
     * @param endX Ending X position
     * @param duration Duration in milliseconds
     */
    public static void slideInFromLeft(String id, float startX, float endX, long duration) {
        startAnimation(id, startX, endX, duration, EasingType.EASE_OUT);
    }

    /**
     * Creates a pulsing animation
     * @param id Animation identifier
     * @param minValue Minimum value
     * @param maxValue Maximum value
     * @param duration Duration for one pulse cycle
     */
    public static void startPulse(String id, float minValue, float maxValue, long duration) {
        // This would need a more complex implementation for continuous pulsing
        startAnimation(id, minValue, maxValue, duration, EasingType.EASE_IN_OUT);
    }
}
