# Action Bar Logger

A Minecraft Fabric mod for version 1.21.1 that captures and logs action bar messages to a file with timestamps.

## Features

- **Client-side only**: No server installation required
- **Automatic logging**: Captures all action bar messages with timestamps
- **Configurable**: Enable/disable logging, custom log file location, and more
- **Log rotation**: Automatic log file rotation when size limit is reached
- **Thread-safe**: Asynchronous logging to prevent game performance impact

### 🆕 Enhanced Visual Status Display
- **Advanced Tier Detection**: Automatically detects "Mood Swings I-V" messages with tier parsing
- **Real-time Status Display**: Shows current mood status with tier, countdown timer, and effect descriptions
- **Modern GUI**: Comprehensive settings screen with live preview and extensive customization
- **Smart Positioning**: Configurable position, scale, anchor points, and draggable repositioning
- **Rich Visual Elements**: Progress bars, mood icons, tier-based effect calculations, and modern styling
- **Keybind Support**: Press 'R' key to open settings screen (configurable)

## Installation

1. Install Fabric Loader for Minecraft 1.21.1
2. Install Fabric API
3. Place the mod JAR file in your `mods` folder
4. Launch Minecraft

## Configuration

The mod creates a configuration file at `config/actionbar-logger.json` with the following options:

```json
{
  "enabled": true,
  "logFilePath": "config/actionbar-logger/actionbar.log",
  "includeTimestamp": true,
  "createDirectories": true,
  "maxLogFileSize": 10,
  "rotateLogFiles": true,
  "showStatusDisplay": true,
  "statusX": -1,
  "statusY": 50,
  "statusScale": 1.0,
  "isDraggable": true
}
```

### Configuration Options

- `enabled`: Enable or disable action bar logging
- `logFilePath`: Path to the log file (relative to game directory or absolute)
- `includeTimestamp`: Include timestamps in log entries
- `createDirectories`: Automatically create directories if they don't exist
- `maxLogFileSize`: Maximum log file size in MB before rotation
- `rotateLogFiles`: Enable automatic log file rotation

#### Status Display Options
- `showStatusDisplay`: Enable or disable the visual status display
- `statusX`: X position of status box (-1 for center, or pixel position)
- `statusY`: Y position of status box (pixels from top)
- `statusScale`: Scale factor for the status display (1.0 = normal size)
- `isDraggable`: Allow dragging the status box to reposition it

## Log Format

Log entries are formatted as:
```
[YYYY-MM-DD HH:MM:SS] Action Bar: <message>
```

Example:
```
[2025-06-04 21:55:30] Action Bar: Health: 20/20
[2025-06-04 21:55:31] Action Bar: XP: 1250/2000
[2025-06-04 22:44:43] Action Bar: Mood Swings IV has made you feel lazy!
```

## Status Display Format

When mood swing messages are detected, a visual status box appears showing:

```
┌─────────────────────────────────┐
│ 🐼 Panda Boots Status           │
│ 😴 LAZY IV - 8s                 │
│ ▓▓▓▓▓▓▓▓░░ 80%                 │
│ You are moving 22% slower       │
└─────────────────────────────────┘
```

**Status Colors:**
- **LAZY**: Blue (#4A90E2)
- **AGGRESSIVE**: Red (#E74C3C)
- **PLAYFUL**: Green (#2ECC71)

**Enhanced Features:**
- **Tier Detection**: Supports tiers I through V with different effect percentages
- **Progress Bar**: Visual countdown with percentage display
- **Effect Descriptions**: Tier-based damage/speed calculations
- **Mood Icons**: Unicode emoji icons for each mood type
- **10-second countdown timer** for each status
- **Draggable positioning** (click and drag the box)
- **Comprehensive settings screen** (press 'R' key)
- **Automatic expiration** when timer reaches 0
- **Modern styling** with configurable shadows, borders, and transparency

## Testing Instructions

### Development Environment Setup

1. Clone this repository
2. Open terminal in the project directory
3. Run `./gradlew build` to build the mod
4. Run `./gradlew runClient` to test in development environment

### Testing Action Bar Messages

To verify the mod is working correctly, test with these methods:

#### Method 1: Commands (Creative/Operator)
```
/title @s actionbar {"text":"Test Action Bar Message","color":"yellow"}
```

#### Method 1b: Test Enhanced Mood Swing Messages (All Tiers)
```
# Test all tier levels
/title @s actionbar {"text":"Mood Swings I has made you feel lazy!","color":"blue"}
/title @s actionbar {"text":"Mood Swings II has made you feel aggressive!","color":"red"}
/title @s actionbar {"text":"Mood Swings III has made you feel playful!","color":"green"}
/title @s actionbar {"text":"Mood Swings IV has made you feel lazy!","color":"blue"}
/title @s actionbar {"text":"Mood Swings V has made you feel aggressive!","color":"red"}

# Test effect descriptions
/title @s actionbar {"text":"Mood Swings V has made you feel aggressive!","color":"red"}
# Should show: "You are doing 30% more damage"

/title @s actionbar {"text":"Mood Swings I has made you feel lazy!","color":"blue"}
# Should show: "You are moving 28% slower"
```

#### Method 2: Mods/Plugins
Many mods and server plugins send action bar messages:
- Boss health bars
- Status indicators
- Custom HUD elements
- Server announcements

#### Method 3: Game Events
Some vanilla game events trigger action bar messages:
- Eating food (hunger/saturation display)
- Tool durability warnings
- Effect notifications

### Verification Steps

1. **Check log file creation**: 
   - Look for `config/actionbar-logger/actionbar.log` in your game directory
   - Verify the directory is created automatically

2. **Test message capture**:
   - Send test action bar messages using commands
   - Check that messages appear in the log file with timestamps

3. **Test enhanced status display**:
   - Use mood swing test commands above
   - Verify status box appears with tier, color, countdown, and progress bar
   - Test effect descriptions show correct percentages for each tier
   - Test dragging the status box to reposition it
   - Wait 10 seconds to verify automatic expiration
   - Press 'R' key to open settings screen and test customization options

4. **Test configuration**:
   - Modify `config/actionbar-logger.json`
   - Change `showStatusDisplay`, `statusX`, `statusY`, `statusScale`
   - Restart the game and verify changes take effect

5. **Test log rotation**:
   - Set `maxLogFileSize` to 1 MB
   - Generate many action bar messages
   - Verify old log files are rotated with timestamps

## Building

To build the mod from source:

```bash
./gradlew build
```

The built JAR file will be in `build/libs/`.

## Compatibility

- **Minecraft Version**: 1.21.1
- **Fabric Loader**: 0.16.12 or higher
- **Java**: 21 or higher
- **Fabric API**: Required

## Environment

This mod is **client-side only** and works in:
- Single-player worlds
- Multiplayer servers (no server-side installation needed)
- Development environments

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Support

If you encounter issues:
1. Check the game logs for error messages
2. Verify your Fabric and mod versions
3. Test with minimal mod setup
4. Report issues with detailed information
