package net.marko.runicmod.render;

import net.marko.runicmod.RunicMod;
import net.marko.runicmod.config.RunicModConfig;
import net.marko.runicmod.rune.RuneEffect;
import net.marko.runicmod.rune.RuneType;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import org.slf4j.Logger;

import java.util.*;

/**
 * Custom text renderer for displaying multiple rune effects
 */
public class CustomTextRenderer {
    private static final Logger LOGGER = RunicMod.LOGGER;

    // Map of effect IDs to their rune effects
    private static final Map<String, RuneEffect> activeEffects = new LinkedHashMap<>();
    private static final int MAX_EFFECTS = 50; // Increased capacity
    private static final int LINE_SPACING = 16; // Reduced for more compact display
    private static final int COLUMN_SPACING = 20; // Space between columns
    private static final int MAX_TEXT_WIDTH = 180; // Maximum width for text before truncation

    /**
     * Adds a new rune effect
     * @param type The type of rune effect
     * @param playerName The name of the player (for runic obstruction) or null for personal effects
     * @param duration The duration in seconds
     * @param tier The tier of the rune (e.g., "III")
     */
    public static void addRuneEffect(RuneType type, String playerName, int duration, String tier) {
        // Create unique ID for this effect
        String id = type.name() + "_" + (playerName != null ? playerName : "SELF") + "_" + System.currentTimeMillis();

        // Remove oldest effect if we're at max capacity
        if (activeEffects.size() >= MAX_EFFECTS) {
            String oldestKey = activeEffects.keySet().iterator().next();
            activeEffects.remove(oldestKey);
            LOGGER.debug("Removed oldest effect to make room for new one");
        }

        RuneEffect effect = new RuneEffect(id, type, playerName, duration, tier);
        activeEffects.put(id, effect);

        LOGGER.info("Added {} effect: player={}, duration={}, tier={}", type.getDisplayName(), playerName, duration, tier);
    }

    /**
     * Renders all active rune effects
     * @param context The draw context
     */
    public static void renderEffects(DrawContext context) {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player == null) return;

        // Get the config
        RunicModConfig config = RunicModConfig.getInstance();

        if (!config.isShowTitle()) return;

        // Remove expired effects (no animations)
        activeEffects.entrySet().removeIf(entry -> entry.getValue().isExpired());

        // Render active effects
        renderActiveEffects(context, config);
    }

    /**
     * Renders all active effects in a draggable box with multi-column layout
     * @param context The draw context
     * @param config The mod config
     */
    private static void renderActiveEffects(DrawContext context, RunicModConfig config) {
        if (activeEffects.isEmpty()) return;

        int screenWidth = MinecraftClient.getInstance().getWindow().getScaledWidth();
        int screenHeight = MinecraftClient.getInstance().getWindow().getScaledHeight();

        // Calculate layout parameters
        float scale = config.getTitleScale();
        int padding = 10;
        int lineHeight = LINE_SPACING;

        // Determine number of columns based on effect count
        int effectCount = activeEffects.size();
        int columns = calculateColumnCount(effectCount);
        int rowsPerColumn = (int) Math.ceil((double) effectCount / columns);

        // Calculate column width with text truncation
        int columnWidth = Math.min(MAX_TEXT_WIDTH, getMaxTextWidth(scale));
        int totalWidth = (columnWidth * columns) + (COLUMN_SPACING * (columns - 1)) + (padding * 2);
        int totalHeight = (rowsPerColumn * lineHeight) + (padding * 2);

        // Get box position
        int boxX = config.getTitleX();
        int boxY = config.getTitleY();

        // Center horizontally if x is -1
        if (boxX == -1) {
            boxX = (screenWidth / 2) - (totalWidth / 2);
        }

        // Clamp to screen bounds
        boxX = Math.max(0, Math.min(screenWidth - totalWidth, boxX));
        boxY = Math.max(0, Math.min(screenHeight - totalHeight, boxY));

        // Draw box background
        int backgroundColor = 0x80000000; // Semi-transparent black
        int borderColor = 0xFF444444; // Dark gray border

        // Background
        context.fill(boxX, boxY, boxX + totalWidth, boxY + totalHeight, backgroundColor);

        // Border
        context.drawBorder(boxX, boxY, totalWidth, totalHeight, borderColor);

        // Draw column separators
        if (columns > 1) {
            for (int i = 1; i < columns; i++) {
                int separatorX = boxX + padding + (i * (columnWidth + COLUMN_SPACING)) - (COLUMN_SPACING / 2);
                context.fill(separatorX, boxY + padding, separatorX + 1, boxY + totalHeight - padding, 0xFF555555);
            }
        }

        // Render effects in columns
        renderEffectsInColumns(context, activeEffects.values(), boxX + padding, boxY + padding,
                              columnWidth, lineHeight, columns, rowsPerColumn, scale);
    }

    /**
     * Calculates the optimal number of columns based on effect count
     */
    private static int calculateColumnCount(int effectCount) {
        if (effectCount <= 8) return 1;
        if (effectCount <= 20) return 2;
        if (effectCount <= 35) return 3;
        return 4; // Maximum 4 columns
    }

    /**
     * Gets the maximum text width from all effects
     */
    private static int getMaxTextWidth(float scale) {
        int maxWidth = 0;
        for (RuneEffect effect : activeEffects.values()) {
            String text = truncateText(effect.getDisplayText(), MAX_TEXT_WIDTH, scale);
            int textWidth = FontManager.getTextWidth(text, scale);
            maxWidth = Math.max(maxWidth, textWidth);
        }
        return Math.max(maxWidth, 120); // Minimum column width
    }

    /**
     * Truncates text if it exceeds maximum width
     */
    private static String truncateText(String text, int maxWidth, float scale) {
        int textWidth = FontManager.getTextWidth(text, scale);
        if (textWidth <= maxWidth) return text;

        String ellipsis = "...";
        int ellipsisWidth = FontManager.getTextWidth(ellipsis, scale);
        int availableWidth = maxWidth - ellipsisWidth;

        // Binary search for optimal length
        int left = 0, right = text.length();
        while (left < right) {
            int mid = (left + right + 1) / 2;
            String truncated = text.substring(0, mid);
            if (FontManager.getTextWidth(truncated, scale) <= availableWidth) {
                left = mid;
            } else {
                right = mid - 1;
            }
        }

        return text.substring(0, left) + ellipsis;
    }

    /**
     * Renders effects in a multi-column layout
     */
    private static void renderEffectsInColumns(DrawContext context, java.util.Collection<RuneEffect> effects,
                                             int startX, int startY, int columnWidth, int lineHeight,
                                             int columns, int rowsPerColumn, float scale) {
        int effectIndex = 0;
        for (RuneEffect effect : effects) {
            int column = effectIndex / rowsPerColumn;
            int row = effectIndex % rowsPerColumn;

            if (column >= columns) break; // Safety check

            int x = startX + (column * (columnWidth + COLUMN_SPACING));
            int y = startY + (row * lineHeight);

            String text = truncateText(effect.getDisplayText(), MAX_TEXT_WIDTH, scale);
            int color = effect.getType().getColor();

            FontManager.drawText(context, text, x, y, color, scale, false);
            effectIndex++;
        }
    }

    // Dragging state
    private static boolean isDragging = false;
    private static int dragStartX = 0;
    private static int dragStartY = 0;
    private static int boxStartX = 0;
    private static int boxStartY = 0;

    /**
     * Gets the current box bounds for dragging
     * @return int array [x, y, width, height] or null if no effects
     */
    public static int[] getBoxBounds() {
        if (activeEffects.isEmpty()) return null;

        RunicModConfig config = RunicModConfig.getInstance();
        int screenWidth = MinecraftClient.getInstance().getWindow().getScaledWidth();

        // Calculate layout parameters (same as renderActiveEffects)
        float scale = config.getTitleScale();
        int padding = 10;
        int lineHeight = LINE_SPACING;

        // Determine number of columns based on effect count
        int effectCount = activeEffects.size();
        int columns = calculateColumnCount(effectCount);
        int rowsPerColumn = (int) Math.ceil((double) effectCount / columns);

        // Calculate dimensions
        int columnWidth = Math.min(MAX_TEXT_WIDTH, getMaxTextWidth(scale));
        int totalWidth = (columnWidth * columns) + (COLUMN_SPACING * (columns - 1)) + (padding * 2);
        int totalHeight = (rowsPerColumn * lineHeight) + (padding * 2);

        // Get box position
        int boxX = config.getTitleX();
        int boxY = config.getTitleY();

        // Center horizontally if x is -1
        if (boxX == -1) {
            boxX = (screenWidth / 2) - (totalWidth / 2);
        }

        return new int[]{boxX, boxY, totalWidth, totalHeight};
    }

    /**
     * Handles mouse click for dragging
     * @param mouseX Mouse X position
     * @param mouseY Mouse Y position
     * @return true if click was handled
     */
    public static boolean handleMouseClick(double mouseX, double mouseY) {
        int[] bounds = getBoxBounds();
        if (bounds == null) return false;

        int boxX = bounds[0];
        int boxY = bounds[1];
        int boxWidth = bounds[2];
        int boxHeight = bounds[3];

        // Check if click is within box bounds
        if (mouseX >= boxX && mouseX <= boxX + boxWidth &&
            mouseY >= boxY && mouseY <= boxY + boxHeight) {

            isDragging = true;
            dragStartX = (int) mouseX;
            dragStartY = (int) mouseY;
            boxStartX = boxX;
            boxStartY = boxY;
            return true;
        }

        return false;
    }

    /**
     * Handles mouse drag for moving the box
     * @param mouseX Mouse X position
     * @param mouseY Mouse Y position
     */
    public static void handleMouseDrag(double mouseX, double mouseY) {
        if (!isDragging) return;

        RunicModConfig config = RunicModConfig.getInstance();
        int screenWidth = MinecraftClient.getInstance().getWindow().getScaledWidth();
        int screenHeight = MinecraftClient.getInstance().getWindow().getScaledHeight();

        int[] bounds = getBoxBounds();
        if (bounds == null) return;

        int boxWidth = bounds[2];
        int boxHeight = bounds[3];

        // Calculate new position
        int newX = boxStartX + ((int) mouseX - dragStartX);
        int newY = boxStartY + ((int) mouseY - dragStartY);

        // Clamp to screen bounds
        newX = Math.max(0, Math.min(screenWidth - boxWidth, newX));
        newY = Math.max(0, Math.min(screenHeight - boxHeight, newY));

        // Update config
        config.setTitleX(newX);
        config.setTitleY(newY);
    }

    /**
     * Handles mouse release to stop dragging
     */
    public static void handleMouseRelease() {
        isDragging = false;
    }

    /**
     * Checks if currently dragging
     * @return true if dragging
     */
    public static boolean isDragging() {
        return isDragging;
    }

    /**
     * Applies alpha to a color
     * @param color The base color
     * @param alpha Alpha value (0.0 to 1.0)
     * @return Color with applied alpha
     */
    private static int applyAlpha(int color, float alpha) {
        int a = (int) (255 * alpha);
        int r = (color >> 16) & 0xFF;
        int g = (color >> 8) & 0xFF;
        int b = color & 0xFF;
        return (a << 24) | (r << 16) | (g << 8) | b;
    }

    /**
     * Clears all active effects
     */
    public static void clearAllEffects() {
        activeEffects.clear();
        isDragging = false; // Stop any dragging
        LOGGER.info("Cleared all rune effects");
    }

    /**
     * Gets the number of active effects
     * @return Number of active effects
     */
    public static int getActiveEffectCount() {
        return activeEffects.size();
    }

    /**
     * Legacy method for compatibility
     * @deprecated Use addRuneEffect instead
     */
    @Deprecated
    public static void startCountdown(String playerName, int duration) {
        addRuneEffect(RuneType.RUNIC_OBSTRUCTION, playerName, duration, "");
    }

    /**
     * Legacy method for compatibility
     * @deprecated Use clearAllEffects instead
     */
    @Deprecated
    public static void clearCountdowns() {
        clearAllEffects();
    }
}
