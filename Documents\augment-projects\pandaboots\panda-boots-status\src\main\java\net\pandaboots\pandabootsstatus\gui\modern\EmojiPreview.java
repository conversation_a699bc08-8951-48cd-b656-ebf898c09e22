package net.pandaboots.pandabootsstatus.gui.modern;

import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.text.Text;
import net.minecraft.util.Identifier;
import net.pandaboots.pandabootsstatus.render.CustomEmojiLoader;
import net.pandaboots.pandabootsstatus.render.SafeEmojiRenderer;

/**
 * Widget that shows a preview of an emoji (custom or fallback)
 */
public class EmojiPreview extends ModernWidget {
    private final String emojiName;
    private final String label;
    private boolean showFallback = true;

    // Preview size (larger than actual emoji for visibility)
    private static final int PREVIEW_SIZE = 24;

    public EmojiPreview(int x, int y, String emojiName, String label) {
        super(x, y, PREVIEW_SIZE + 60, PREVIEW_SIZE + 10, Text.literal("Emoji Preview"));
        this.emojiName = emojiName;
        this.label = label;
    }

    @Override
    protected void onPress() {
        // No action needed for preview
    }
    
    @Override
    protected void renderContent(DrawContext context, int mouseX, int mouseY, float delta) {
        // Draw label
        context.drawText(MinecraftClient.getInstance().textRenderer, label, getX() + PREVIEW_SIZE + 8, getY() + 2, TEXT_COLOR, false);

        // Draw preview area background
        int previewX = getX() + 4;
        int previewY = getY() + 4;
        context.fill(previewX - 1, previewY - 1, previewX + PREVIEW_SIZE + 1, previewY + PREVIEW_SIZE + 1, 0xFF333333);
        
        // Try to render emoji preview
        boolean rendered = false;
        
        // Try custom emoji first
        if (CustomEmojiLoader.hasCustomEmoji(emojiName)) {
            try {
                Identifier customTexture = CustomEmojiLoader.getCustomEmojiTexture(emojiName);
                if (customTexture != null) {
                    context.drawTexture(customTexture, previewX, previewY, 0, 0, PREVIEW_SIZE, PREVIEW_SIZE, PREVIEW_SIZE, PREVIEW_SIZE);
                    rendered = true;
                    
                    // Show "Custom" status
                    context.drawText(MinecraftClient.getInstance().textRenderer, "Custom", getX() + PREVIEW_SIZE + 8, getY() + 14, 0x44FF44, false);
                }
            } catch (Exception e) {
                // Fall through to fallback
            }
        }
        
        // Check if custom emoji is loading
        if (!rendered && CustomEmojiLoader.isLoading(emojiName)) {
            // Draw loading indicator
            drawLoadingIndicator(context, previewX, previewY);
            context.drawText(MinecraftClient.getInstance().textRenderer, "Loading...", getX() + PREVIEW_SIZE + 8, getY() + 14, 0xFFAA00, false);
            rendered = true;
        }
        
        // Fall back to default emoji if enabled and no custom emoji
        if (!rendered && showFallback) {
            try {
                // Render fallback emoji (scaled up for preview)
                float scale = (float) PREVIEW_SIZE / 8.0f; // Scale up from 8px to preview size
                SafeEmojiRenderer.renderEmoji(context, emojiName, previewX, previewY, scale);
                
                // Show fallback status
                String fallbackType = SafeEmojiRenderer.getCurrentRenderingMethod();
                if (fallbackType.contains("External PNG")) {
                    context.drawText(MinecraftClient.getInstance().textRenderer, "PNG", getX() + PREVIEW_SIZE + 8, getY() + 14, 0x4A90E2, false);
                } else if (fallbackType.contains("Generated")) {
                    context.drawText(MinecraftClient.getInstance().textRenderer, "Generated", getX() + PREVIEW_SIZE + 8, getY() + 14, 0x9B59B6, false);
                } else {
                    context.drawText(MinecraftClient.getInstance().textRenderer, "Unicode", getX() + PREVIEW_SIZE + 8, getY() + 14, 0xE67E22, false);
                }
                rendered = true;
            } catch (Exception e) {
                // Fall through to error state
            }
        }
        
        // Show error state if nothing rendered
        if (!rendered) {
            drawErrorIndicator(context, previewX, previewY);
            context.drawText(MinecraftClient.getInstance().textRenderer, "Error", getX() + PREVIEW_SIZE + 8, getY() + 14, 0xFF4444, false);
        }
    }
    
    private void drawLoadingIndicator(DrawContext context, int x, int y) {
        // Draw spinning loading indicator
        long time = System.currentTimeMillis();
        float rotation = (time % 2000) / 2000.0f * 360.0f;
        
        // Simple loading animation - draw rotating dots
        int centerX = x + PREVIEW_SIZE / 2;
        int centerY = y + PREVIEW_SIZE / 2;
        
        for (int i = 0; i < 8; i++) {
            float angle = (rotation + i * 45) * (float) Math.PI / 180.0f;
            int dotX = centerX + (int) (Math.cos(angle) * 8);
            int dotY = centerY + (int) (Math.sin(angle) * 8);
            
            int alpha = (int) (255 * (1.0f - i / 8.0f));
            int color = (alpha << 24) | 0xFFAA00;
            
            context.fill(dotX - 1, dotY - 1, dotX + 1, dotY + 1, color);
        }
    }
    
    private void drawErrorIndicator(DrawContext context, int x, int y) {
        // Draw red X
        int centerX = x + PREVIEW_SIZE / 2;
        int centerY = y + PREVIEW_SIZE / 2;
        
        // Draw X pattern
        for (int i = -6; i <= 6; i++) {
            context.fill(centerX + i - 1, centerY + i - 1, centerX + i + 1, centerY + i + 1, 0xFFFF4444);
            context.fill(centerX + i - 1, centerY - i - 1, centerX + i + 1, centerY - i + 1, 0xFFFF4444);
        }
    }
    
    /**
     * Refresh the preview (useful when custom emoji status changes)
     */
    public void refresh() {
        // This method can be called to trigger a re-render
        // The actual refresh happens in the render method
    }
    
    /**
     * Set whether to show fallback emoji when custom emoji is not available
     */
    public void setShowFallback(boolean showFallback) {
        this.showFallback = showFallback;
    }
    
    /**
     * Get the current emoji status
     */
    public String getEmojiStatus() {
        if (CustomEmojiLoader.hasCustomEmoji(emojiName)) {
            return "Custom emoji loaded";
        } else if (CustomEmojiLoader.isLoading(emojiName)) {
            return "Loading custom emoji...";
        } else {
            return "Using fallback emoji";
        }
    }
}
