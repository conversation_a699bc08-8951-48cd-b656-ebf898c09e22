{"schemaVersion": 1, "id": "actionbar-logger", "version": "1.0.0", "name": "Action Bar Logger", "description": "A client-side mod that captures and logs action bar messages to a file with timestamps", "authors": ["PandaBoots"], "contact": {"homepage": "https://github.com/pandaboots/actionbar-logger", "sources": "https://github.com/pandaboots/actionbar-logger"}, "license": "MIT", "icon": "assets/actionbar-logger/icon.png", "environment": "client", "entrypoints": {"client": ["net.pandaboots.actionbarlogger.ActionBarLogger"]}, "mixins": ["actionbar-logger.mixins.json"], "depends": {"fabricloader": ">=0.16.12", "minecraft": "~1.21.1", "java": ">=21", "fabric-api": "*"}, "suggests": {}}