package net.marko.runicmod.gui.modern;

import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.widget.ClickableWidget;
import net.minecraft.client.gui.screen.narration.NarrationMessageBuilder;
import net.minecraft.text.Text;
import java.util.ArrayList;
import java.util.List;

/**
 * Base class for modern UI widgets with consistent styling
 */
public abstract class ModernWidget extends ClickableWidget {
    
    // Modern color scheme - Contemporary dark theme with high contrast
    public static final int PRIMARY_COLOR = 0xFF1A1A1A;      // Deep dark background
    public static final int SECONDARY_COLOR = 0xFF2A2A2A;    // Secondary background for cards
    public static final int BACKGROUND_COLOR = 0xF0121212;   // Main background with opacity
    public static final int BACKGROUND_SECONDARY = 0xF01E1E1E; // Card background

    public static final int BORDER_COLOR = 0xFF2D2D2D;       // Subtle border color
    public static final int BORDER_ACCENT = 0xFF404040;      // Accent border for focus states
    public static final int BORDER_FOCUS = 0xFF3B82F6;       // Focus border (blue)

    public static final int TEXT_COLOR = 0xFFE8E8E8;         // High contrast white text
    public static final int TEXT_SECONDARY = 0xFFB0B0B0;     // Secondary text with good contrast
    public static final int TEXT_MUTED = 0xFF808080;         // Muted text for less important info

    public static final int ACCENT_COLOR = 0xFF3B82F6;       // Modern blue accent (Tailwind blue-500)
    public static final int ACCENT_HOVER = 0xFF2563EB;       // Darker blue for hover (Tailwind blue-600)
    public static final int SUCCESS_COLOR = 0xFF10B981;      // Modern green (Tailwind emerald-500)
    public static final int WARNING_COLOR = 0xFFF59E0B;      // Modern amber (Tailwind amber-500)
    public static final int ERROR_COLOR = 0xFFEF4444;        // Modern red (Tailwind red-500)

    public static final int HOVER_COLOR = 0xFF2A2A2A;        // Subtle hover background
    public static final int ACTIVE_COLOR = 0xFF363636;       // Active/pressed state
    public static final int FOCUS_COLOR = 0xFF4A90E2;        // Focus ring color

    // Shadow and depth colors
    public static final int SHADOW_COLOR = 0x40000000;       // Subtle shadow
    public static final int SHADOW_STRONG = 0x60000000;      // Stronger shadow for elevation

    // Spacing and layout constants
    public static final int BORDER_RADIUS = 6;               // Modern rounded corners
    public static final int PADDING_SMALL = 8;               // Small padding
    public static final int PADDING_MEDIUM = 12;             // Medium padding
    public static final int PADDING_LARGE = 16;              // Large padding
    public static final int SPACING_SMALL = 4;               // Small spacing between elements
    public static final int SPACING_MEDIUM = 8;              // Medium spacing
    public static final int SPACING_LARGE = 12;              // Large spacing
    
    protected boolean isHovered = false;
    protected boolean isPressed = false;
    
    public ModernWidget(int x, int y, int width, int height, Text message) {
        super(x, y, width, height, message);
    }
    
    @Override
    public void renderWidget(DrawContext context, int mouseX, int mouseY, float delta) {
        // Update hover state
        isHovered = mouseX >= getX() && mouseY >= getY() && 
                   mouseX < getX() + getWidth() && mouseY < getY() + getHeight();
        
        renderBackground(context, mouseX, mouseY, delta);
        renderContent(context, mouseX, mouseY, delta);
        renderBorder(context, mouseX, mouseY, delta);
    }
    
    /**
     * Renders the widget background with modern styling
     */
    protected void renderBackground(DrawContext context, int mouseX, int mouseY, float delta) {
        int backgroundColor = getBackgroundColor();

        // Draw subtle shadow for depth
        if (isHovered || isPressed) {
            context.fill(getX() + 1, getY() + 1, getX() + getWidth() + 1, getY() + getHeight() + 1, SHADOW_COLOR);
        }

        // Draw main background with rounded corners effect (simulated with border)
        context.fill(getX(), getY(), getX() + getWidth(), getY() + getHeight(), backgroundColor);

        // Add subtle inner highlight for modern look
        if (isHovered) {
            context.fill(getX() + 1, getY() + 1, getX() + getWidth() - 1, getY() + 2, 0x20FFFFFF);
        }
    }
    
    /**
     * Renders the widget content (text, icons, etc.)
     */
    protected abstract void renderContent(DrawContext context, int mouseX, int mouseY, float delta);
    
    /**
     * Renders the widget border with modern styling
     */
    protected void renderBorder(DrawContext context, int mouseX, int mouseY, float delta) {
        int borderColor = getBorderColor();

        // Draw main border
        context.drawBorder(getX(), getY(), getWidth(), getHeight(), borderColor);

        // Add focus ring for accessibility
        if (isFocused()) {
            context.drawBorder(getX() - 1, getY() - 1, getWidth() + 2, getHeight() + 2, BORDER_FOCUS);
        }
    }

    /**
     * Gets the background color based on widget state
     */
    protected int getBackgroundColor() {
        if (isPressed) return ACTIVE_COLOR;
        if (isHovered) return HOVER_COLOR;
        return PRIMARY_COLOR;
    }

    /**
     * Gets the border color based on widget state
     */
    protected int getBorderColor() {
        if (isPressed) return ACCENT_HOVER;
        if (isHovered) return ACCENT_COLOR;
        return BORDER_COLOR;
    }
    
    /**
     * Gets the text color based on widget state
     */
    protected int getTextColor() {
        return TEXT_COLOR;
    }
    
    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button == 0 && isHovered) {
            isPressed = true;
            return true;
        }
        return false;
    }
    
    @Override
    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        if (button == 0 && isPressed) {
            isPressed = false;
            if (isHovered) {
                onPress();
            }
            return true;
        }
        return false;
    }
    
    /**
     * Called when the widget is pressed
     */
    protected abstract void onPress();
    
    /**
     * Draws centered text with proper scaling
     */
    protected void drawCenteredText(DrawContext context, String text, int color) {
        var textRenderer = MinecraftClient.getInstance().textRenderer;
        int textWidth = textRenderer.getWidth(text);
        int textX = getX() + (getWidth() - textWidth) / 2;
        int textY = getY() + (getHeight() - textRenderer.fontHeight) / 2;
        context.drawText(textRenderer, text, textX, textY, color, false);
    }

    /**
     * Draws left-aligned text with proper scaling
     */
    protected void drawLeftText(DrawContext context, String text, int x, int y, int color) {
        var textRenderer = MinecraftClient.getInstance().textRenderer;
        context.drawText(textRenderer, text, x, y, color, false);
    }

    /**
     * Advanced text fitting with wrapping, scaling, and fallback truncation
     */
    protected void drawFittedText(DrawContext context, String text, int x, int y, int maxWidth, int maxHeight, int color, TextAlignment alignment) {
        var textRenderer = MinecraftClient.getInstance().textRenderer;

        // First try: Check if text fits as-is
        if (textRenderer.getWidth(text) <= maxWidth && textRenderer.fontHeight <= maxHeight) {
            drawAlignedText(context, text, x, y, maxWidth, color, alignment, 1.0f);
            return;
        }

        // Second try: Text wrapping for multi-line capable widgets
        if (maxHeight >= textRenderer.fontHeight * 2) {
            List<String> wrappedLines = wrapText(textRenderer, text, maxWidth);
            int totalHeight = wrappedLines.size() * textRenderer.fontHeight;

            if (totalHeight <= maxHeight) {
                drawWrappedText(context, wrappedLines, x, y, maxWidth, maxHeight, color, alignment);
                return;
            }
        }

        // Third try: Dynamic font scaling
        float scale = calculateOptimalScale(textRenderer, text, maxWidth, maxHeight);
        if (scale >= 0.7f) { // Minimum readable scale
            drawAlignedText(context, text, x, y, maxWidth, color, alignment, scale);
            return;
        }

        // Last resort: Intelligent truncation
        String truncatedText = truncateTextIntelligently(textRenderer, text, maxWidth);
        drawAlignedText(context, truncatedText, x, y, maxWidth, color, alignment, 1.0f);
    }

    /**
     * Text alignment options
     */
    protected enum TextAlignment {
        LEFT, CENTER, RIGHT
    }

    /**
     * Sets the Y position of the widget
     */
    public void setY(int y) {
        // Use the parent's setY method to avoid infinite recursion
        super.setY(y);
    }

    /**
     * Wraps text into multiple lines that fit within the specified width
     */
    protected List<String> wrapText(net.minecraft.client.font.TextRenderer textRenderer, String text, int maxWidth) {
        List<String> lines = new ArrayList<>();
        String[] words = text.split(" ");
        StringBuilder currentLine = new StringBuilder();

        for (String word : words) {
            String testLine = currentLine.length() == 0 ? word : currentLine + " " + word;

            if (textRenderer.getWidth(testLine) <= maxWidth) {
                currentLine = new StringBuilder(testLine);
            } else {
                if (currentLine.length() > 0) {
                    lines.add(currentLine.toString());
                    currentLine = new StringBuilder(word);
                } else {
                    // Single word is too long, force break
                    lines.add(word);
                }
            }
        }

        if (currentLine.length() > 0) {
            lines.add(currentLine.toString());
        }

        return lines.isEmpty() ? List.of("") : lines;
    }

    /**
     * Calculates optimal scale factor for text to fit within bounds
     */
    protected float calculateOptimalScale(net.minecraft.client.font.TextRenderer textRenderer, String text, int maxWidth, int maxHeight) {
        int textWidth = textRenderer.getWidth(text);
        int textHeight = textRenderer.fontHeight;

        float widthScale = (float) maxWidth / textWidth;
        float heightScale = (float) maxHeight / textHeight;

        return Math.min(widthScale, heightScale);
    }

    /**
     * Draws text with specified alignment and scale
     */
    protected void drawAlignedText(DrawContext context, String text, int x, int y, int maxWidth, int color, TextAlignment alignment, float scale) {
        var textRenderer = MinecraftClient.getInstance().textRenderer;

        // Calculate scaled dimensions
        int scaledWidth = (int) (textRenderer.getWidth(text) * scale);
        int scaledHeight = (int) (textRenderer.fontHeight * scale);

        // Calculate position based on alignment
        int textX = x;
        switch (alignment) {
            case CENTER:
                textX = x + (maxWidth - scaledWidth) / 2;
                break;
            case RIGHT:
                textX = x + maxWidth - scaledWidth;
                break;
            case LEFT:
            default:
                textX = x;
                break;
        }

        // Apply scaling and draw
        if (scale != 1.0f) {
            context.getMatrices().push();
            context.getMatrices().translate(textX, y, 0);
            context.getMatrices().scale(scale, scale, 1.0f);
            context.drawText(textRenderer, text, 0, 0, color, false);
            context.getMatrices().pop();
        } else {
            context.drawText(textRenderer, text, textX, y, color, false);
        }
    }

    /**
     * Draws wrapped text with proper vertical centering
     */
    protected void drawWrappedText(DrawContext context, List<String> lines, int x, int y, int maxWidth, int maxHeight, int color, TextAlignment alignment) {
        var textRenderer = MinecraftClient.getInstance().textRenderer;
        int lineHeight = textRenderer.fontHeight;
        int totalHeight = lines.size() * lineHeight;

        // Center the text block vertically
        int startY = y + (maxHeight - totalHeight) / 2;

        for (int i = 0; i < lines.size(); i++) {
            String line = lines.get(i);
            int lineY = startY + i * lineHeight;
            drawAlignedText(context, line, x, lineY, maxWidth, color, alignment, 1.0f);
        }
    }

    /**
     * Intelligent text truncation with word boundary awareness
     */
    protected String truncateTextIntelligently(net.minecraft.client.font.TextRenderer textRenderer, String text, int maxWidth) {
        if (textRenderer.getWidth(text) <= maxWidth) {
            return text;
        }

        String ellipsis = "...";
        int ellipsisWidth = textRenderer.getWidth(ellipsis);

        if (maxWidth <= ellipsisWidth) {
            return "";
        }

        int availableWidth = maxWidth - ellipsisWidth;

        // Try to break at word boundaries first
        String[] words = text.split(" ");
        StringBuilder result = new StringBuilder();

        for (String word : words) {
            String testText = result.length() == 0 ? word : result + " " + word;
            if (textRenderer.getWidth(testText) <= availableWidth) {
                result = new StringBuilder(testText);
            } else {
                break;
            }
        }

        // If we got at least one word, use it
        if (result.length() > 0) {
            return result + ellipsis;
        }

        // Otherwise, character-level truncation
        for (int i = text.length() - 1; i >= 0; i--) {
            String substring = text.substring(0, i);
            if (textRenderer.getWidth(substring) <= availableWidth) {
                return substring + ellipsis;
            }
        }

        return ellipsis;
    }

    @Override
    protected void appendClickableNarrations(NarrationMessageBuilder builder) {
        builder.put(net.minecraft.client.gui.screen.narration.NarrationPart.TITLE, getMessage());
    }
}
