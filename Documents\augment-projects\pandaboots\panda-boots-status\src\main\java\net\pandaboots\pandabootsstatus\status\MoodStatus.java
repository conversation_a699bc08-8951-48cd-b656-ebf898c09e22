package net.pandaboots.pandabootsstatus.status;

/**
 * Represents the different mood statuses from Panda Boots
 */
public enum MoodStatus {
    LAZY("LAZY", "😴", "sleepy"),
    AGGRESSIVE("AGGRESSIVE", "😡", "angry"), // Changed to angry face emoji
    PLAYFUL("PLAYFUL", "😄", "happy");

    private final String displayName;
    private final String icon;
    private final String emojiName; // For the new emoji renderer

    MoodStatus(String displayName, String icon, String emojiName) {
        this.displayName = displayName;
        this.icon = icon;
        this.emojiName = emojiName;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getIcon() {
        return icon;
    }

    public String getEmojiName() {
        return emojiName;
    }

    /**
     * Parses a mood status from a string
     * @param text The text to parse
     * @return The mood status or null if not found
     */
    public static MoodStatus fromString(String text) {
        if (text == null) return null;
        
        String lowerText = text.toLowerCase();
        for (MoodStatus status : values()) {
            if (lowerText.contains(status.displayName.toLowerCase())) {
                return status;
            }
        }
        return null;
    }

    /**
     * Gets the display text for the status with countdown and tier
     * @param tier The tier level (1-5)
     * @param remainingSeconds The remaining seconds
     * @param showMoodIcons Whether to show mood icons
     * @return Formatted display text
     */
    public String getDisplayText(int tier, int remainingSeconds, boolean showMoodIcons) {
        String tierRoman = convertToRoman(tier);
        String iconPart = showMoodIcons ? icon + " " : "";
        return iconPart + displayName + " " + tierRoman + " " + remainingSeconds + "s";
    }

    /**
     * Gets the display text for the status with countdown and tier (with icons enabled by default)
     * @param tier The tier level (1-5)
     * @param remainingSeconds The remaining seconds
     * @return Formatted display text
     */
    public String getDisplayText(int tier, int remainingSeconds) {
        return getDisplayText(tier, remainingSeconds, true);
    }

    /**
     * Converts a tier number to Roman numerals
     * @param tier The tier number (1-5)
     * @return Roman numeral string
     */
    private String convertToRoman(int tier) {
        switch (tier) {
            case 1: return "I";
            case 2: return "II";
            case 3: return "III";
            case 4: return "IV";
            case 5: return "V";
            default: return String.valueOf(tier);
        }
    }

    /**
     * Gets the effect description for this mood at the given tier
     * @param tier The tier level (1-5)
     * @return Effect description
     */
    public String getEffectDescription(int tier) {
        switch (this) {
            case AGGRESSIVE:
                // Fixed formula: 5% base + 5% × (tier - 1)
                // Tier I: 5%, Tier II: 10%, Tier III: 15%, Tier IV: 20%, Tier V: 25%
                int aggressiveDamage = 5 + (5 * (tier - 1));
                return "You are doing " + aggressiveDamage + "% more damage";
            case PLAYFUL:
                // Fixed formula: 5% base + 5% × (tier - 1)
                // Tier I: 5%, Tier II: 10%, Tier III: 15%, Tier IV: 20%, Tier V: 25%
                int playfulSpeed = 5 + (5 * (tier - 1));
                return "You are moving " + playfulSpeed + "% faster";
            case LAZY:
                // Formula: 30% base - 2% × tier
                // Tier I: 28%, Tier II: 26%, Tier III: 24%, Tier IV: 22%, Tier V: 20%
                int lazySpeed = 30 - (2 * tier);
                return "You are moving " + lazySpeed + "% slower";
            default:
                return "";
        }
    }

    /**
     * Checks if a message is a mood swing message
     * @param message The message to check
     * @return true if it's a mood swing message
     */
    public static boolean isMoodSwingMessage(String message) {
        if (message == null) return false;
        return message.toLowerCase().contains("mood swings") &&
               (message.toLowerCase().contains("lazy") ||
                message.toLowerCase().contains("aggressive") ||
                message.toLowerCase().contains("playful"));
    }

    /**
     * Parses a mood swing message to extract status and tier
     * @param message The message to parse
     * @return MoodParseResult or null if parsing failed
     */
    public static MoodParseResult parseMessage(String message) {
        if (message == null) return null;

        // Pattern: "Mood Swings [I-V]+ has made you feel [mood]!"
        String pattern = "Mood Swings ([IVX]+) has made you feel (lazy|aggressive|playful)!";
        java.util.regex.Pattern regex = java.util.regex.Pattern.compile(pattern, java.util.regex.Pattern.CASE_INSENSITIVE);
        java.util.regex.Matcher matcher = regex.matcher(message);

        if (matcher.find()) {
            String tierRoman = matcher.group(1);
            String moodText = matcher.group(2);

            int tier = parseRomanNumeral(tierRoman);
            MoodStatus status = fromString(moodText);

            if (tier > 0 && status != null) {
                return new MoodParseResult(status, tier);
            }
        }

        return null;
    }

    /**
     * Parses Roman numerals (I-V) to integers
     * @param roman The Roman numeral string
     * @return Integer value or 0 if invalid
     */
    private static int parseRomanNumeral(String roman) {
        if (roman == null) return 0;

        switch (roman.toUpperCase()) {
            case "I": return 1;
            case "II": return 2;
            case "III": return 3;
            case "IV": return 4;
            case "V": return 5;
            default: return 0;
        }
    }

    /**
     * Result class for mood message parsing
     */
    public static class MoodParseResult {
        private final MoodStatus status;
        private final int tier;

        public MoodParseResult(MoodStatus status, int tier) {
            this.status = status;
            this.tier = tier;
        }

        public MoodStatus getStatus() {
            return status;
        }

        public int getTier() {
            return tier;
        }
    }
}
