package net.pandaboots.pandabootsstatus.render;

import net.minecraft.client.MinecraftClient;
import net.minecraft.client.texture.NativeImage;
import net.minecraft.client.texture.NativeImageBackedTexture;
import net.minecraft.util.Identifier;
import net.pandaboots.pandabootsstatus.PandaBootsStatus;
import net.pandaboots.pandabootsstatus.config.ActionBarConfig;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Loads custom emoji images from URLs or file paths
 */
public class CustomEmojiLoader {
    private static final Map<String, Identifier> customTextures = new HashMap<>();
    private static final Map<String, CompletableFuture<Boolean>> loadingTasks = new HashMap<>();
    private static final HttpClient httpClient = HttpClient.newBuilder()
            .connectTimeout(Duration.ofSeconds(10))
            .build();
    
    private static final int TARGET_SIZE = 8; // Half size emojis
    private static boolean initialized = false;
    
    /**
     * Initializes the custom emoji loader
     */
    public static void initialize() {
        if (initialized) return;
        
        ActionBarConfig config = ActionBarConfig.getInstance();
        if (!config.isUseCustomEmojis()) {
            PandaBootsStatus.LOGGER.info("Custom emojis disabled in config");
            initialized = true;
            return;
        }
        
        // Load custom emojis asynchronously
        loadCustomEmoji("panda", config.getCustomPandaEmojiUrl());
        loadCustomEmoji("aggressive", config.getCustomAggressiveEmojiUrl());
        loadCustomEmoji("playful", config.getCustomPlayfulEmojiUrl());
        loadCustomEmoji("lazy", config.getCustomLazyEmojiUrl());
        
        initialized = true;
        PandaBootsStatus.LOGGER.info("Custom emoji loader initialized");
    }
    
    /**
     * Loads a custom emoji from URL or file path
     */
    private static void loadCustomEmoji(String emojiName, String urlOrPath) {
        if (urlOrPath == null || urlOrPath.trim().isEmpty()) {
            PandaBootsStatus.LOGGER.debug("No custom URL/path configured for emoji: {}", emojiName);
            return;
        }
        
        String source = urlOrPath.trim();
        PandaBootsStatus.LOGGER.info("Loading custom emoji {} from: {}", emojiName, source);
        
        // Start async loading
        CompletableFuture<Boolean> loadingTask = CompletableFuture.supplyAsync(() -> {
            try {
                NativeImage image = loadImageFromSource(source);
                if (image != null) {
                    registerCustomTexture(emojiName, image);
                    return true;
                }
            } catch (Exception e) {
                PandaBootsStatus.LOGGER.warn("Failed to load custom emoji {} from {}: {}", emojiName, source, e.getMessage());
            }
            return false;
        });
        
        loadingTasks.put(emojiName, loadingTask);
        
        // Log completion
        loadingTask.thenAccept(success -> {
            if (success) {
                PandaBootsStatus.LOGGER.info("Successfully loaded custom emoji: {}", emojiName);
            } else {
                PandaBootsStatus.LOGGER.warn("Failed to load custom emoji: {}", emojiName);
            }
        });
    }
    
    /**
     * Loads an image from URL or file path
     */
    private static NativeImage loadImageFromSource(String source) throws Exception {
        InputStream inputStream = null;
        
        try {
            if (source.startsWith("http://") || source.startsWith("https://")) {
                // Load from URL
                inputStream = downloadFromUrl(source);
            } else {
                // Load from file path
                Path filePath = Paths.get(source);
                if (!Files.exists(filePath)) {
                    throw new Exception("File not found: " + source);
                }
                inputStream = Files.newInputStream(filePath);
            }
            
            if (inputStream == null) {
                throw new Exception("Failed to get input stream for: " + source);
            }
            
            // Convert to NativeImage
            return convertToNativeImage(inputStream);
            
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (Exception e) {
                    // Ignore close errors
                }
            }
        }
    }
    
    /**
     * Downloads image from URL
     */
    private static InputStream downloadFromUrl(String url) throws Exception {
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .timeout(Duration.ofSeconds(30))
                .header("User-Agent", "PandaBootsStatus/1.0")
                .build();
        
        HttpResponse<byte[]> response = httpClient.send(request, HttpResponse.BodyHandlers.ofByteArray());
        
        if (response.statusCode() != 200) {
            throw new Exception("HTTP " + response.statusCode() + " when downloading: " + url);
        }
        
        return new ByteArrayInputStream(response.body());
    }
    
    /**
     * Converts InputStream to NativeImage with proper sizing
     */
    private static NativeImage convertToNativeImage(InputStream inputStream) throws Exception {
        // Read as BufferedImage first for resizing
        BufferedImage bufferedImage = ImageIO.read(inputStream);
        if (bufferedImage == null) {
            throw new Exception("Failed to read image data");
        }
        
        // Resize to target size if needed
        if (bufferedImage.getWidth() != TARGET_SIZE || bufferedImage.getHeight() != TARGET_SIZE) {
            BufferedImage resized = new BufferedImage(TARGET_SIZE, TARGET_SIZE, BufferedImage.TYPE_INT_ARGB);
            java.awt.Graphics2D g2d = resized.createGraphics();
            g2d.setRenderingHint(java.awt.RenderingHints.KEY_INTERPOLATION, java.awt.RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g2d.drawImage(bufferedImage, 0, 0, TARGET_SIZE, TARGET_SIZE, null);
            g2d.dispose();
            bufferedImage = resized;
        }
        
        // Convert to PNG bytes
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(bufferedImage, "PNG", baos);
        byte[] pngBytes = baos.toByteArray();
        
        // Create NativeImage from PNG bytes
        return NativeImage.read(new ByteArrayInputStream(pngBytes));
    }
    
    /**
     * Registers a custom texture with Minecraft
     */
    private static void registerCustomTexture(String emojiName, NativeImage image) {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client == null) {
            PandaBootsStatus.LOGGER.warn("Client not available for texture registration: {}", emojiName);
            return;
        }
        
        try {
            NativeImageBackedTexture texture = new NativeImageBackedTexture(image);
            Identifier textureId = Identifier.of("panda-boots-status", "custom_emoji_" + emojiName);
            
            client.getTextureManager().registerTexture(textureId, texture);
            customTextures.put(emojiName, textureId);
            
            PandaBootsStatus.LOGGER.debug("Registered custom texture: {} -> {}", emojiName, textureId);
        } catch (Exception e) {
            PandaBootsStatus.LOGGER.error("Failed to register custom texture for {}: {}", emojiName, e.getMessage());
        }
    }
    
    /**
     * Checks if a custom emoji is available
     */
    public static boolean hasCustomEmoji(String emojiName) {
        return customTextures.containsKey(emojiName);
    }
    
    /**
     * Gets the texture identifier for a custom emoji
     */
    public static Identifier getCustomEmojiTexture(String emojiName) {
        return customTextures.get(emojiName);
    }
    
    /**
     * Checks if a custom emoji is still loading
     */
    public static boolean isLoading(String emojiName) {
        CompletableFuture<Boolean> task = loadingTasks.get(emojiName);
        return task != null && !task.isDone();
    }
    
    /**
     * Reloads custom emojis (for when config changes)
     */
    public static void reload() {
        // Clear existing textures
        customTextures.clear();
        loadingTasks.clear();
        initialized = false;
        
        // Reinitialize
        initialize();
    }
    
    /**
     * Gets debug information about loaded custom emojis
     */
    public static String getDebugInfo() {
        StringBuilder info = new StringBuilder();
        info.append("Custom Emoji Loader Status:\n");
        info.append("Initialized: ").append(initialized).append("\n");
        info.append("Loaded textures: ").append(customTextures.size()).append("\n");
        
        for (Map.Entry<String, Identifier> entry : customTextures.entrySet()) {
            info.append("  ").append(entry.getKey()).append(": ").append(entry.getValue()).append("\n");
        }
        
        info.append("Loading tasks: ").append(loadingTasks.size()).append("\n");
        for (Map.Entry<String, CompletableFuture<Boolean>> entry : loadingTasks.entrySet()) {
            info.append("  ").append(entry.getKey()).append(": ")
                .append(entry.getValue().isDone() ? "Complete" : "Loading").append("\n");
        }
        
        return info.toString();
    }
    
    /**
     * Validates if a URL or file path is potentially valid
     */
    public static boolean isValidSource(String source) {
        if (source == null || source.trim().isEmpty()) {
            return false;
        }
        
        String trimmed = source.trim();
        
        // Check URL format
        if (trimmed.startsWith("http://") || trimmed.startsWith("https://")) {
            try {
                URI.create(trimmed);
                return true;
            } catch (Exception e) {
                return false;
            }
        }
        
        // Check file path format
        try {
            Path path = Paths.get(trimmed);
            return path.isAbsolute() || !trimmed.contains(".."); // Basic security check
        } catch (Exception e) {
            return false;
        }
    }
}
