package net.marko.runicmod.gui.modern;

import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.Element;
import net.minecraft.client.gui.Selectable;
import net.minecraft.client.gui.widget.ClickableWidget;
import net.minecraft.client.gui.screen.narration.NarrationMessageBuilder;
import net.minecraft.text.Text;

import java.util.ArrayList;
import java.util.List;

/**
 * A scrollable container widget that can hold multiple child widgets
 */
public class ScrollableContainer extends ClickableWidget {
    
    private final List<ModernWidget> children = new ArrayList<>();
    private int scrollOffset = 0;
    private int contentHeight = 0;
    private boolean isScrollable = false;
    private boolean isDragging = false;
    private int dragStartY = 0;
    private int scrollStartOffset = 0;
    
    // Scrollbar constants
    private static final int SCROLLBAR_WIDTH = 6;
    private static final int SCROLLBAR_MARGIN = 2;
    private static final int MIN_SCROLLBAR_HEIGHT = 20;
    
    public ScrollableContainer(int x, int y, int width, int height) {
        super(x, y, width, height, Text.empty());
    }
    
    /**
     * Adds a child widget to the container
     */
    public void addChild(ModernWidget child) {
        children.add(child);
        updateContentHeight();
    }
    
    /**
     * Clears all child widgets
     */
    public void clearChildren() {
        children.clear();
        scrollOffset = 0;
        contentHeight = 0;
        isScrollable = false;
    }
    
    /**
     * Updates the total content height and scrollable state
     */
    private void updateContentHeight() {
        if (children.isEmpty()) {
            contentHeight = 0;
            isScrollable = false;
            return;
        }
        
        int maxY = 0;
        for (ModernWidget child : children) {
            int childBottom = child.getY() + child.getHeight() - getY();
            maxY = Math.max(maxY, childBottom);
        }
        
        contentHeight = maxY;
        isScrollable = contentHeight > getHeight();
        
        // Clamp scroll offset if content shrunk
        if (scrollOffset > getMaxScrollOffset()) {
            scrollOffset = Math.max(0, getMaxScrollOffset());
        }
    }
    
    /**
     * Gets the maximum scroll offset
     */
    private int getMaxScrollOffset() {
        return Math.max(0, contentHeight - getHeight());
    }
    
    /**
     * Scrolls by the specified amount
     */
    public void scroll(int amount) {
        if (!isScrollable) return;
        
        int newOffset = scrollOffset + amount;
        scrollOffset = Math.max(0, Math.min(getMaxScrollOffset(), newOffset));
    }
    
    @Override
    public void renderWidget(DrawContext context, int mouseX, int mouseY, float delta) {
        // Draw container background
        context.fill(getX(), getY(), getX() + getWidth(), getY() + getHeight(), ModernWidget.BACKGROUND_SECONDARY);
        context.drawBorder(getX(), getY(), getWidth(), getHeight(), ModernWidget.BORDER_COLOR);
        
        // Set up clipping for scrollable content
        int contentWidth = isScrollable ? getWidth() - SCROLLBAR_WIDTH - SCROLLBAR_MARGIN : getWidth();
        
        // Enable scissor test for clipping
        context.enableScissor(getX(), getY(), getX() + contentWidth, getY() + getHeight());
        
        // Render children with scroll offset
        for (ModernWidget child : children) {
            int childY = child.getY() - scrollOffset;
            
            // Only render if child is visible in the viewport
            if (childY + child.getHeight() >= getY() && childY <= getY() + getHeight()) {
                // Temporarily adjust child position for rendering
                int originalY = child.getY();
                child.setY(childY);
                child.render(context, mouseX, mouseY, delta);
                child.setY(originalY); // Restore original position
            }
        }
        
        // Disable scissor test
        context.disableScissor();
        
        // Draw scrollbar if needed
        if (isScrollable) {
            drawScrollbar(context, mouseX, mouseY);
        }
    }
    
    /**
     * Draws the scrollbar
     */
    private void drawScrollbar(DrawContext context, int mouseX, int mouseY) {
        int scrollbarX = getX() + getWidth() - SCROLLBAR_WIDTH;
        int scrollbarY = getY();
        int scrollbarHeight = getHeight();
        
        // Draw scrollbar track
        context.fill(scrollbarX, scrollbarY, scrollbarX + SCROLLBAR_WIDTH, scrollbarY + scrollbarHeight, 
                    ModernWidget.BORDER_COLOR);
        
        // Calculate scrollbar thumb
        float scrollRatio = (float) scrollOffset / getMaxScrollOffset();
        float thumbRatio = (float) getHeight() / contentHeight;
        
        int thumbHeight = Math.max(MIN_SCROLLBAR_HEIGHT, (int) (scrollbarHeight * thumbRatio));
        int thumbY = scrollbarY + (int) ((scrollbarHeight - thumbHeight) * scrollRatio);
        
        // Draw scrollbar thumb
        boolean isHoveringScrollbar = mouseX >= scrollbarX && mouseX <= scrollbarX + SCROLLBAR_WIDTH &&
                                     mouseY >= thumbY && mouseY <= thumbY + thumbHeight;
        
        int thumbColor = isHoveringScrollbar || isDragging ? ModernWidget.ACCENT_COLOR : ModernWidget.TEXT_SECONDARY;
        context.fill(scrollbarX, thumbY, scrollbarX + SCROLLBAR_WIDTH, thumbY + thumbHeight, thumbColor);
    }
    
    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button != 0) return false;
        
        // Check if clicking on scrollbar
        if (isScrollable) {
            int scrollbarX = getX() + getWidth() - SCROLLBAR_WIDTH;
            if (mouseX >= scrollbarX && mouseX <= scrollbarX + SCROLLBAR_WIDTH &&
                mouseY >= getY() && mouseY <= getY() + getHeight()) {
                
                isDragging = true;
                dragStartY = (int) mouseY;
                scrollStartOffset = scrollOffset;
                return true;
            }
        }
        
        // Check children (adjust for scroll offset)
        for (ModernWidget child : children) {
            int adjustedChildY = child.getY() - scrollOffset;
            if (mouseX >= child.getX() && mouseX <= child.getX() + child.getWidth() &&
                mouseY >= adjustedChildY && mouseY <= adjustedChildY + child.getHeight()) {
                
                // Temporarily adjust child position for click handling
                int originalY = child.getY();
                child.setY(adjustedChildY);
                boolean result = child.mouseClicked(mouseX, mouseY, button);
                child.setY(originalY);
                return result;
            }
        }
        
        return false;
    }
    
    @Override
    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        if (button == 0 && isDragging) {
            isDragging = false;
            return true;
        }
        
        // Forward to children
        for (ModernWidget child : children) {
            int adjustedChildY = child.getY() - scrollOffset;
            int originalY = child.getY();
            child.setY(adjustedChildY);
            child.mouseReleased(mouseX, mouseY, button);
            child.setY(originalY);
        }
        
        return false;
    }
    
    @Override
    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        if (isDragging && isScrollable) {
            int dragDistance = (int) mouseY - dragStartY;
            float scrollRatio = (float) dragDistance / (getHeight() - MIN_SCROLLBAR_HEIGHT);
            int newOffset = scrollStartOffset + (int) (scrollRatio * getMaxScrollOffset());
            scrollOffset = Math.max(0, Math.min(getMaxScrollOffset(), newOffset));
            return true;
        }
        
        return false;
    }
    
    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double horizontalAmount, double verticalAmount) {
        if (isScrollable && mouseX >= getX() && mouseX <= getX() + getWidth() &&
            mouseY >= getY() && mouseY <= getY() + getHeight()) {
            
            scroll((int) (-verticalAmount * 20)); // Scroll speed multiplier
            return true;
        }
        
        return false;
    }
    
    /**
     * Gets the current scroll offset
     */
    public int getScrollOffset() {
        return scrollOffset;
    }
    
    /**
     * Sets the scroll offset
     */
    public void setScrollOffset(int offset) {
        this.scrollOffset = Math.max(0, Math.min(getMaxScrollOffset(), offset));
    }
    
    /**
     * Checks if the container is currently scrollable
     */
    public boolean isScrollable() {
        return isScrollable;
    }
    
    @Override
    protected void appendClickableNarrations(NarrationMessageBuilder builder) {
        builder.put(net.minecraft.client.gui.screen.narration.NarrationPart.TITLE, Text.literal("Scrollable container"));
    }
}
