package net.pandaboots.actionbarlogger.status;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Enum representing different mood swing statuses that can be detected
 */
public enum MoodStatus {
    LAZY("LAZY", 0x4A90E2, "😴"),      // Blue
    AGGRESSIVE("AGGRESSIVE", 0xE74C3C, "😠"), // Red
    PLAYFUL("PLAYFUL", 0x2ECC71, "😄");       // Green

    private final String displayName;
    private final int color;
    private final String icon;

    // Enhanced regex pattern for tier detection
    private static final Pattern MOOD_PATTERN = Pattern.compile(
        "mood\\s+swings\\s+([ivx]+)\\s+has\\s+made\\s+you\\s+feel\\s+(lazy|aggressive|playful)!?",
        Pattern.CASE_INSENSITIVE
    );

    MoodStatus(String displayName, int color, String icon) {
        this.displayName = displayName;
        this.color = color;
        this.icon = icon;
    }

    public String getDisplayName() {
        return displayName;
    }

    public int getColor() {
        return color;
    }

    public String getIcon() {
        return icon;
    }

    /**
     * Gets the display text for the status with countdown and tier
     * @param tier The tier level (1-5)
     * @param remainingSeconds The remaining seconds
     * @return Formatted display text
     */
    public String getDisplayText(int tier, int remainingSeconds) {
        String tierRoman = convertToRoman(tier);
        return icon + " " + displayName + " " + tierRoman + " - " + remainingSeconds + "s";
    }

    /**
     * Gets the effect description based on tier
     * @param tier The tier level (1-5)
     * @return Effect description text
     */
    public String getEffectDescription(int tier) {
        switch (this) {
            case AGGRESSIVE:
                int aggressiveDamage = 5 + (5 * tier); // 10%, 15%, 20%, 25%, 30%
                return "You are doing " + aggressiveDamage + "% more damage";
            case PLAYFUL:
                int playfulSpeed = 5 + (5 * tier); // 10%, 15%, 20%, 25%, 30%
                return "You are moving " + playfulSpeed + "% faster";
            case LAZY:
                int lazySpeed = 30 - (2 * tier); // 28%, 26%, 24%, 22%, 20%
                return "You are moving " + lazySpeed + "% slower";
            default:
                return "";
        }
    }

    /**
     * Parses a mood status and tier from action bar message text
     * @param message The action bar message
     * @return MoodParseResult with status and tier, or null if none found
     */
    public static MoodParseResult parseMessage(String message) {
        if (message == null) return null;

        Matcher matcher = MOOD_PATTERN.matcher(message);
        if (matcher.find()) {
            String tierString = matcher.group(1);
            String moodString = matcher.group(2);

            int tier = parseRomanNumeral(tierString);
            MoodStatus status = parseMoodType(moodString);

            if (tier > 0 && status != null) {
                return new MoodParseResult(status, tier);
            }
        }

        return null;
    }

    /**
     * Checks if a message is a mood swing message
     * @param message The action bar message
     * @return true if it's a mood swing message, false otherwise
     */
    public static boolean isMoodSwingMessage(String message) {
        if (message == null) return false;
        return MOOD_PATTERN.matcher(message).find();
    }

    /**
     * Parses Roman numeral to integer (I-V = 1-5)
     * @param roman The Roman numeral string
     * @return Integer value, or 0 if invalid
     */
    private static int parseRomanNumeral(String roman) {
        if (roman == null) return 0;

        switch (roman.toUpperCase()) {
            case "I": return 1;
            case "II": return 2;
            case "III": return 3;
            case "IV": return 4;
            case "V": return 5;
            default: return 0;
        }
    }

    /**
     * Converts integer to Roman numeral (1-5 = I-V)
     * @param number The integer value
     * @return Roman numeral string
     */
    private static String convertToRoman(int number) {
        switch (number) {
            case 1: return "I";
            case 2: return "II";
            case 3: return "III";
            case 4: return "IV";
            case 5: return "V";
            default: return "?";
        }
    }

    /**
     * Parses mood type from string
     * @param moodString The mood string
     * @return MoodStatus or null if invalid
     */
    private static MoodStatus parseMoodType(String moodString) {
        if (moodString == null) return null;

        switch (moodString.toLowerCase()) {
            case "lazy": return LAZY;
            case "aggressive": return AGGRESSIVE;
            case "playful": return PLAYFUL;
            default: return null;
        }
    }

    /**
     * Result class for mood parsing
     */
    public static class MoodParseResult {
        private final MoodStatus status;
        private final int tier;

        public MoodParseResult(MoodStatus status, int tier) {
            this.status = status;
            this.tier = tier;
        }

        public MoodStatus getStatus() {
            return status;
        }

        public int getTier() {
            return tier;
        }
    }
}
