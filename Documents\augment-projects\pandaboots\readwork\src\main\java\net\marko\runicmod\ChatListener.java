package net.marko.runicmod;

import net.fabricmc.fabric.api.client.message.v1.ClientReceiveMessageEvents;
import net.marko.runicmod.networking.RunicNetworking;
import net.marko.runicmod.sound.SoundManager;
import net.marko.runicmod.rune.RuneType;
import net.marko.runicmod.render.CustomTextRenderer;
import net.marko.runicmod.config.RunicModConfig;
import net.minecraft.client.MinecraftClient;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.math.Vec3d;
import org.slf4j.Logger;
import java.util.regex.Pattern;
import java.util.regex.Matcher;
import java.util.HashMap;
import java.util.Map;

public class ChatListener {
    private static final String PREFIX = "!";
    private static final long MESSAGE_COOLDOWN_MS = 350;
    private static long lastMessageTime = 0;
    private static final Logger LOGGER = RunicMod.LOGGER;

    // Store the current message being processed
    private static String currentMessage = null;

    // Track active runic obstructions with player names and end times
    private static final Map<String, Long> activeRunicObstructions = new HashMap<>();

    public static void register() {
        ClientReceiveMessageEvents.ALLOW_GAME.register((message, overlay) -> {
            long currentTime = System.currentTimeMillis();
            if (currentTime - lastMessageTime < MESSAGE_COOLDOWN_MS) return true;

            String msg = message.getString();

            // Store the current message for use in processing
            currentMessage = msg;

            // Log all incoming chat messages at debug level
            LOGGER.debug("Received chat message: {}", msg);

            // Try to detect different rune patterns
            detectRunePatterns(msg);

            return true;
        });
    }

    /**
     * Detects various rune patterns in chat messages
     * @param msg The chat message to analyze
     */
    private static void detectRunePatterns(String msg) {
        RunicModConfig config = RunicModConfig.getInstance();

        // Pattern 1: Sky Stepper cooldown
        // "RUNES ✔ Stepped! Your Sky Stepper III is usable again in: 9s"
        if (config.isEnableSkyStepperDetection()) {
            Pattern skyStepperPattern = Pattern.compile("RUNES ✔ Stepped! Your Sky Stepper ([IVX]+) is usable again in: (\\d+)s");
            Matcher skyStepperMatcher = skyStepperPattern.matcher(msg);
            if (skyStepperMatcher.find()) {
                String tier = skyStepperMatcher.group(1);
                int duration = Integer.parseInt(skyStepperMatcher.group(2));

                if (duration >= 1 && duration <= 60) {
                    processRuneEffect(RuneType.SKY_STEPPER, null, duration, tier);
                    return;
                }
            }
        }

        // Pattern 2: Dasher cooldown
        // "RUNES ✔ Dashed! Your Dasher III is usable again in: 9s"
        if (config.isEnableDasherDetection()) {
            Pattern dasherPattern = Pattern.compile("RUNES ✔ Dashed! Your Dasher ([IVX]+) is usable again in: (\\d+)s");
            Matcher dasherMatcher = dasherPattern.matcher(msg);
            if (dasherMatcher.find()) {
                String tier = dasherMatcher.group(1);
                int duration = Integer.parseInt(dasherMatcher.group(2));

                if (duration >= 1 && duration <= 60) {
                    processRuneEffect(RuneType.DASHER, null, duration, tier);
                    return;
                }
            }
        }

        // Pattern 3: Rune Obstruction - Original format
        // "rune effects of [player] with your Rune Obstruction"
        if (config.isEnableRunicObstruction() && msg.contains("rune effects of") && msg.contains("with your Rune Obstruction")) {
            Pattern originalPattern = Pattern.compile("rune effects of ([^\\s]+).*for (\\d+) sec");
            Matcher originalMatcher = originalPattern.matcher(msg);
            if (originalMatcher.find()) {
                String playerName = originalMatcher.group(1);
                int duration = Integer.parseInt(originalMatcher.group(2));

                if (duration >= 1 && duration <= 6) {
                    processRuneEffect(RuneType.RUNIC_OBSTRUCTION, playerName, duration, "");
                    return;
                }
            }
        }

        // Pattern 4: Rune Obstruction - New format
        // "RUNES ✔ You have disabled [player]'s rune effects with your Rune Obstruction [tier] for [duration] sec"
        if (config.isEnableRunicObstruction()) {
            Pattern newRunicPattern = Pattern.compile("RUNES ✔ You have disabled ([^']+)'s rune effects with your Rune Obstruction ([IVX]+) for (\\d+) sec");
            Matcher newRunicMatcher = newRunicPattern.matcher(msg);
            if (newRunicMatcher.find()) {
                String playerName = newRunicMatcher.group(1);
                String tier = newRunicMatcher.group(2);
                int duration = Integer.parseInt(newRunicMatcher.group(3));

                if (duration >= 1 && duration <= 6 && isValidRunicLevel(tier)) {
                    processRuneEffect(RuneType.RUNIC_OBSTRUCTION, playerName, duration, tier);
                    return;
                }
            }
        }

        // Pattern 5: Mod communication format
        // "! [player] has been runed for [duration]"
        if (config.isEnableRunicObstruction() && msg.startsWith(PREFIX + " ") && msg.contains("has been runed for")) {
            Pattern modCommPattern = Pattern.compile("(?:" + Pattern.quote(PREFIX) + "\\s+)?([^\\s]+)\\s+has been runed for\\s+(\\d+)");
            Matcher modCommMatcher = modCommPattern.matcher(msg);
            if (modCommMatcher.find()) {
                String playerName = modCommMatcher.group(1);
                int duration = Integer.parseInt(modCommMatcher.group(2));

                if (duration >= 1 && duration <= 6) {
                    processRuneEffect(RuneType.RUNIC_OBSTRUCTION, playerName, duration, "");
                    return;
                }
            }
        }

        // Pattern 6: Runic Obstruction expiration
        // "RUNES ✔ The effect of your Runic Obstruction... has worn off"
        if (msg.contains("RUNES ✔ The effect of your Runic Obstruction") && msg.contains("has worn off")) {
            LOGGER.debug("Detected runic obstruction expiration message");
            // Could add logic here to remove specific effects if needed
        }
    }

    /**
     * Processes a detected rune effect
     * @param type The type of rune effect
     * @param playerName The affected player name (null for personal effects)
     * @param duration The duration in seconds
     * @param tier The tier/level of the rune
     */
    private static void processRuneEffect(RuneType type, String playerName, int duration, String tier) {
        LOGGER.info("Detected {} - Player: {}, Duration: {}s, Tier: {}",
                   type.getDisplayName(), playerName != null ? playerName : "SELF", duration, tier);

        // Store in active obstructions for tracking (for runic obstruction only)
        if (type == RuneType.RUNIC_OBSTRUCTION && playerName != null) {
            long endTime = System.currentTimeMillis() + (duration * 1000L);
            activeRunicObstructions.put(playerName, endTime);

            // Note: Removed automatic chat message sending to prevent spam
            // The mod will only process incoming messages from other mod users
        }

        // Get max distance from config
        int maxDistance = RunicModConfig.getInstance().getMaxDistance();

        // Check distance for runic obstruction effects on other players
        if (type == RuneType.RUNIC_OBSTRUCTION && playerName != null) {
            if (!isPlayerWithinRange(playerName, maxDistance)) {
                LOGGER.debug("Player {} is too far away, not showing effects", playerName);
                return;
            }
        }

        // Add the effect to the display system
        CustomTextRenderer.addRuneEffect(type, playerName, duration, tier);

        // Play sound based on rune type
        SoundManager.playSound(type, RunicModConfig.getInstance().getSoundVolume());

        // Update networking
        if (type == RuneType.RUNIC_OBSTRUCTION && playerName != null) {
            RunicNetworking.sendRunicStatusPacket(playerName, duration);
        }
    }

    /**
     * Sends a formatted chat message to communicate runic status to other clients with the mod
     * DISABLED: This functionality has been removed to prevent automatic chat spam
     */
    /*
    private static void sendServerChatMessage(String playerName, String duration) {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player == null || client.getNetworkHandler() == null) return;

        // Check if we've recently sent a message to avoid duplicates
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastMessageTime < MESSAGE_COOLDOWN_MS) {
            LOGGER.debug("Skipping message due to cooldown");
            return;
        }

        // Format: "! (name) has been runiced for (duration)"
        String message = PREFIX + " " + playerName + " has been runiced for " + duration;
        lastMessageTime = currentTime;
        client.getNetworkHandler().sendChatMessage(message);
        LOGGER.debug("Sent runic status message: {}", message);
    }
    */

    /**
     * Checks if a runic level is valid (between I and X for Sky Stepper/Dasher, I and VI for Runic Obstruction)
     * @param level The runic level in Roman numerals
     * @return true if the level is valid, false otherwise
     */
    private static boolean isValidRunicLevel(String level) {
        switch (level) {
            case "I":
            case "II":
            case "III":
            case "IV":
            case "V":
            case "VI":
            case "VII":
            case "VIII":
            case "IX":
            case "X":
                return true;
            default:
                return false;
        }
    }

    /**
     * Checks if a player is within the specified range of the client player
     * @param playerName The name of the player to check
     * @param range The maximum distance in blocks
     * @return true if the player is within range, false otherwise
     */
    private static boolean isPlayerWithinRange(String playerName, double range) {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player == null || client.world == null) return false;

        // Get the client player's position
        Vec3d clientPos = client.player.getPos();

        // Find the target player
        for (PlayerEntity player : client.world.getPlayers()) {
            if (player.getName().getString().equals(playerName)) {
                // Calculate distance
                Vec3d playerPos = player.getPos();
                double distance = clientPos.distanceTo(playerPos);

                LOGGER.debug("Distance to player {}: {} blocks", playerName, distance);

                // Check if within range
                return distance <= range;
            }
        }

        // Player not found in the world
        LOGGER.debug("Player {} not found in the world", playerName);
        return false;
    }



    /**
     * Cleans up resources when the mod is unloaded
     */
    public static void shutdown() {
        // Clear active obstructions
        activeRunicObstructions.clear();
        LOGGER.info("ChatListener shutdown complete");
    }
}